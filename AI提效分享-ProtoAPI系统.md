# AI提效实践分享：ProtoAPI系统的开发与应用

## 🎯 分享概述

本次分享将从两个维度展示AI在软件开发中的提效实践：

1. **AI辅助开发**：如何使用AI工具高效构建复杂系统
2. **AI功能应用**：如何在系统中集成AI能力提升用户效率

---

## 📊 项目背景与痛点

### 传统API文档管理的困境

![传统API管理痛点对比图 - 建议放置YApi vs ProtoAPI的对比截图]

**YApi等传统工具的局限性：**

- 📝 **一次性维护**：初次导入后需手动维护，开发者往往不会持续更新
- 🔗 **缺乏关联性**：Proto结构体在多接口间复用时失去关联信息
- ⚡ **同步滞后**：接口文档与实际代码容易脱节
- 🚫 **技术局限**：无法充分利用Proto协议的优势

### ProtoAPI系统的解决方案

**核心价值：**

- 🔄 **自动同步**：直接从公司Proto平台实时获取最新接口定义
- 🎯 **完美关联**：保持Proto文件中结构体的复用关系
- 🚀 **零维护成本**：开发者无需手动维护文档
- 🔧 **深度集成**：专为腾讯go-trpc技术栈优化

---

## 🤖 AI辅助开发实践

### 技术栈选择的AI友好性

![技术栈架构图 - 建议放置系统架构图，突出TypeScript和React]

**为什么选择TypeScript + React？**

```typescript
// TypeScript的类型安全让AI能够精准理解代码结构
interface ApiTemplate {
  id: number
  title: string
  prompt: string
  auth: 'PUBLIC' | 'PRIVATE' | 'OPEN'
}

// AI能够根据类型定义自动生成正确的代码
const createTemplate = (data: ApiTemplate) => {
  // AI通过类型推断避免字段错误
}
```

**AI编码优势：**

- 🎯 **类型推断**：TypeScript的类型系统帮助AI理解代码意图
- 🔧 **实时纠错**：编译时错误让AI能够立即发现并修复问题
- 📚 **生态成熟**：React/TypeScript拥有最丰富的AI训练数据
- ⚡ **快速迭代**：AI能够基于类型定义快速生成组件和API

### 开发工具组合

**主力工具：Cursor + Augment Code**

- 💡 **Cursor**：AI原生的代码编辑器，提供智能代码补全和重构
- 🧠 **Augment Code**：深度理解代码库上下文，提供精准的代码建议
- 🔄 **协同效应**：两者结合覆盖从代码编写到架构优化的全流程

### 开发效率数据

**项目规模：**

- 📊 **代码量**：前后端共计 15,000+ 行代码
- ⏱️ **开发周期**：3个月完成（传统开发预估需6-8个月）
- 🤖 **AI贡献**：约80%的代码由AI生成或辅助完成

![开发效率对比图 - 建议制作传统开发vs AI辅助开发的时间对比图]

---

## 🎨 系统核心功能展示

### 1. 项目管理与可视化

![项目列表页面截图 - 展示项目分组和管理界面]

**功能亮点：**

- 📁 **智能分组**：按Git仓库自动分组项目
- 👥 **团队协作**：成员权限管理和访问控制
- 📈 **实时统计**：项目接口数量和变更统计

### 2. 接口文档自动生成

![API文档页面截图 - 展示接口列表和详情页面]

**技术实现：**

- 🔍 **Proto解析**：使用protobuf.js自动解析.proto文件
- 📋 **结构化展示**：自动生成请求/响应参数文档
- 🔗 **关联追踪**：保持消息类型的引用关系

### 3. 接口变更历史对比

![接口变更对比截图 - 展示接口历史变更的可视化对比]

**核心价值：**

- 📊 **可视化对比**：直观展示接口变更内容
- ⏰ **时间范围选择**：支持任意时间段的变更查询
- 🏷️ **变更标识**：新增、修改、删除的清晰标记

---

## 🚀 AI功能集成：智能代码生成

### 模板系统架构

![模板系统架构图 - 展示AI模板和脚本模板的工作流程]

**两种模板类型：**

#### 1. AI模板（基于大语言模型）

```typescript
// AI模板示例：根据API定义生成TypeScript客户端
const prompt = `
你是一个专业的代码生成助手，请根据以下API定义生成TypeScript客户端代码。

要求：
1. 生成类型安全的TypeScript代码
2. 使用fetch API进行网络请求
3. 为每个API方法生成独立函数
4. 添加适当的错误处理

API数据: ${JSON.stringify(apiData)}
`
```

#### 2. 脚本模板（用户自定义逻辑）

```typescript
// 脚本模板示例：生成Go语言的gRPC客户端
function executeScript(apiData) {
  const { serviceName, methodName, requestType, responseType } = apiData

  return `
// ${serviceName} 服务的 ${methodName} 方法客户端
func (c *Client) ${methodName}(ctx context.Context, req *${requestType}) (*${responseType}, error) {
    return c.client.${methodName}(ctx, req)
}
`
}
```

### 代码生成演示

![代码生成界面截图 - 展示模板选择和代码生成过程]

**使用流程：**

1. 🎯 **选择接口**：在接口详情页选择目标API
2. 📝 **选择模板**：从项目绑定的模板中选择
3. ⚡ **一键生成**：AI实时生成代码并自动复制
4. 📋 **即用即走**：生成的代码可直接在项目中使用

---

## 📈 提效成果与价值

### 开发效率提升

**量化指标：**

- ⏱️ **接口对接时间**：从2-3天缩短至30分钟
- 📚 **文档维护成本**：从持续维护降至零成本
- 🔄 **代码生成速度**：从手写1小时缩短至30秒
- 🎯 **错误率降低**：类型安全减少90%的字段错误

### 团队协作改善

![团队协作效果图 - 建议制作前后对比的流程图]

**协作优化：**

- 👥 **前后端对接**：实时同步的接口文档消除沟通成本
- 🔄 **版本管理**：自动追踪接口变更，避免版本不一致
- 📋 **标准化**：统一的代码生成模板确保代码风格一致

### 业务价值延伸

**未来可能性：**

- 🤖 **自动化测试**：基于接口定义自动生成测试用例
- 🔗 **MCP协议集成**：让大模型直接理解项目接口结构
- 🌐 **第三方调用**：为外部系统提供标准化的API访问

---

## ⚠️ 技术限制与适用场景

### 当前限制

**技术约束：**

- 🔧 **技术栈限制**：主要适用于腾讯go-trpc技术栈
- 🏢 **平台依赖**：需要Proto文件同步到trpc.rick.woa.com平台
- 📋 **协议要求**：仅支持基于Protocol Buffers的API

### 适用场景

**最佳实践：**

- ✅ **微服务架构**：大量gRPC接口的项目
- ✅ **团队协作**：需要频繁前后端对接的场景
- ✅ **快速迭代**：接口变更频繁的敏捷开发
- ✅ **标准化需求**：需要统一代码风格的团队

---

## 🎯 AI提效的关键启示

### 1. 选择AI友好的技术栈

- 📚 **生态成熟度**：选择AI训练数据丰富的技术
- 🔧 **类型安全**：强类型系统帮助AI理解代码意图
- 🔄 **实时反馈**：编译时错误让AI能够快速修正

### 2. 工具组合的重要性

- 🎯 **专业化分工**：不同工具解决不同问题
- 🔄 **互补增强**：工具间的协同效应
- 📈 **持续优化**：根据实际使用效果调整工具组合

### 3. AI能力的业务化应用

- 🎨 **用户价值**：AI不仅提升开发效率，更要为最终用户创造价值
- 🔧 **场景适配**：根据具体业务场景设计AI功能
- 📊 **效果量化**：建立明确的效果评估指标

---

## 🚀 总结与展望

ProtoAPI系统展示了AI在软件开发中的双重价值：

1. **开发侧提效**：AI工具帮助快速构建复杂系统
2. **用户侧赋能**：AI功能为用户提供智能化体验

**未来发展方向：**

- 🤖 **更智能的代码生成**：支持更多编程语言和框架
- 🔗 **生态系统集成**：与更多开发工具和平台对接
- 📊 **数据驱动优化**：基于使用数据持续改进AI能力

---

_感谢大家的聆听！欢迎交流讨论AI提效的更多可能性。_

# 正式内容

## 背景与痛点

团队使用 yapi，但是面临的问题，团队技术栈大多使用 trpc go，proto 文件本身携带完整的接口内容，公司有统一的 proto 文件管理平台（rick），但是 yapi 无法直接使用 proto 文件，需要手动维护接口文档。内容脱节。yapi 交互不友好，使用体验差。无法快捷提取接口类型，想借助 ai 大模型快速生成代码的功能。测试同学无法基于 yapi进行接口测试管理，主要是接口不怎么维护，和实际差异过大，yapi的字段格式和 proto本身格式有差异等。

## Proto API 介绍

### 大致原理

采用 trpc-go 的项目，会将 proto 文件同步到 rick 平台，平台会将 proto 存放到 工蜂仓库中。打通工蜂授权，实时拉取最新文件，关联解析 proto，生成接口文档，并可视化展示,可以便捷的查看接口内容，和生成类型定义。

### AI内容提取

在这个基础上，实现了 ai 生成代码能力，支持用户自定义提示词，并通过模板跨项目复用。后端开发，不再需要手动维护接口文档。

## 权限管理

基于 woa 登录认证，并实现了完备的权限管理功能，确保接口数据的安全性。

## AI辅助开发

整个系统，80%的编码由 AI 完成，包括前后端的设计和开发，主要基于 Cursor 和 Augment Code。

## 后续规划

目前全量接口都支持基于 token 的第三方调用，后续可以支持
平台面向 AI，，为后续可以支持大模型 MCP 协议提供基础，能帮助大模型更好的理解项目功能细节和自动编码。
为测试同学提供接口在线测试调用功能，并包括测试用例管理等能力

## 总结（AI提效）
