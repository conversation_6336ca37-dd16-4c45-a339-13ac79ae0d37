# ProtoAPI - Proto文件API管理系统

一个基于现代技术栈的全栈API管理系统，专注于`.proto`文件的解析、文档生成和可视化展示。

## ✨ 项目特性

- 🚀 **Proto文件解析** - 使用protobuf.js解析.proto文件，提取服务、方法、消息结构
- 📚 **自动文档生成** - 自动生成gRPC/Protobuf API文档和可视化界面
- 🎨 **现代化UI** - 基于Mantine UI组件库的美观界面
- 🔄 **实时同步** - 支持实时更新和协同编辑
- 🏗️ **Monorepo架构** - 使用pnpm workspaces管理多包项目
- 🔒 **类型安全** - 端到端TypeScript类型安全
- ⚡ **高性能** - tRPC + TanStack Query提供优秀的开发体验

## 🛠️ 技术栈

### 后端 (`packages/server`)

- **运行时**: Node.js (>=16.0.0)
- **API框架**: tRPC + Express
- **数据库**: MySQL + Prisma ORM
- **Proto解析**: protobuf.js
- **身份认证**: JWT (jose)
- **语言**: TypeScript

### 前端 (`packages/web`)

- **框架**: React 19 + TypeScript
- **路由**: TanStack Router
- **状态管理**: TanStack Query + Zustand
- **UI组件**: Mantine UI v7
- **样式**: Tailwind CSS v4
- **图标**: Tabler Icons
- **数据可视化**: Recharts

### 开发工具

- **包管理**: pnpm (>=8.0.0)
- **构建工具**: Vite + tsup
- **代码质量**: ESLint + Prettier
- **部署**: PM2 + 自定义部署脚本

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- pnpm >= 8.0.0
- MySQL数据库

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd proto-api

# 安装所有依赖（从根目录）
pnpm install
```

### 配置环境

```bash
# 配置服务器环境变量
cd packages/server
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

### 数据库设置

```bash
# 生成Prisma客户端
cd packages/server
pnpm prisma:generate

# 推送数据库模式
pnpm prisma:push

# 可选：运行种子数据
pnpm prisma:seed
```

### 启动开发环境

```bash
# 从根目录启动所有服务
pnpm dev

# 或分别启动
pnpm server:dev  # 启动后端服务 (端口9700)
# 在另一个终端启动前端
cd packages/web && pnpm dev
```

访问应用：

- 前端界面: http://localhost:5173
- API文档: http://localhost:9700/trpc/docs
- tRPC Playground: http://localhost:9700/api/trpc

## 📁 项目结构

```
proto-api/
├── packages/
│   ├── server/                 # 后端API服务
│   │   ├── src/
│   │   │   ├── routes/         # tRPC路由定义
│   │   │   │   ├── proto.ts    # Proto文件相关API
│   │   │   │   ├── project.ts  # 项目管理API
│   │   │   │   ├── user.ts     # 用户管理API
│   │   │   │   └── ...
│   │   │   ├── services/       # 业务逻辑服务
│   │   │   ├── utils/          # 工具函数
│   │   │   ├── schedule/       # 定时任务
│   │   │   ├── trpc.ts         # tRPC配置
│   │   │   └── index.ts        # 服务器入口
│   │   ├── prisma/
│   │   │   └── schema.prisma   # 数据库模式
│   │   └── package.json
│   └── web/                    # 前端React应用
│       ├── src/
│       │   ├── components/     # React组件
│       │   ├── pages/          # 页面组件
│       │   ├── hooks/          # 自定义Hooks
│       │   ├── utils/          # 工具函数
│       │   └── ...
│       └── package.json
├── scripts/                    # 构建和部署脚本
├── package.json               # 根包配置
├── pnpm-workspace.yaml        # Workspace配置
└── README.md
```

## 🎯 核心功能

### 1. Proto文件管理

- 上传和解析.proto文件
- 提取包名、服务、RPC方法、消息结构
- 支持import语句处理
- 版本控制和历史记录

### 2. API文档生成

- 自动生成gRPC服务文档
- 可视化展示消息结构
- 交互式API探索界面
- 支持注释和描述提取

### 3. 项目管理

- 多项目支持
- 团队协作功能
- 权限管理
- 标签和分类系统

### 4. 可视化界面

- 现代化的用户界面
- 响应式设计
- 暗色/亮色主题
- 丰富的交互组件

## 🔧 开发指南

### 添加新依赖

```bash
# 从根目录添加依赖到workspace
pnpm add -w <package-name>

# 添加开发依赖
pnpm add -wD <package-name>
```

### 代码规范

- 使用TypeScript严格模式
- 遵循ESLint和Prettier配置
- 函数式React组件 + Hooks
- tRPC路由使用Zod进行输入验证

### 构建和部署

```bash
# 构建所有包
pnpm build

# 服务器部署（使用PM2）
cd packages/server
pnpm pm2

# 使用部署脚本
pnpm deploy:server  # 部署后端
pnpm deploy:web     # 部署前端
```

## 📖 API文档

项目使用tRPC提供类型安全的API接口。主要路由包括：

- `proto.*` - Proto文件解析和管理
- `project.*` - 项目管理
- `user.*` - 用户认证和管理
- `tag.*` - 标签系统
- `interfaceMetadata.*` - 接口元数据管理

详细API文档可通过访问 `/trpc/docs` 端点查看。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 🐛 问题反馈

如遇到问题或有功能建议，请在 [Issues](../../issues) 页面提交。

---

**注意**: 确保在生产环境中正确配置环境变量和数据库连接。
