# Docs Store 实现完成

## 🎉 实现成果

成功创建了一个专门的 docs store 来管理 API 文档中 curl 命令是否使用真实 token 的设置，并完成了所有相关组件的重构。

## ✅ 主要功能

### 1. DocsStore 设计
- **状态管理**: 使用 Zustand 管理 `useRealToken` 状态
- **持久化**: 使用 sessionStorage 存储设置（临时性设置）
- **简洁API**: 提供 `setUseRealToken` 和 `resetSettings` 方法

### 2. 组件重构
- **TokenSettings**: 移除本地状态管理，直接使用 store
- **ApiProcedureCard**: 移除 props 传递，直接从 store 获取状态
- **ApiTabs**: 简化 props，移除 useRealToken 传递
- **主页面**: 简化状态管理，移除本地状态

### 3. 数据流优化
- **集中管理**: 所有 token 使用设置集中在 store 中
- **自动同步**: 所有组件自动同步最新状态
- **简化传递**: 减少 props 层层传递的复杂性

## 🔧 技术实现

### Store 结构
```typescript
interface DocsState {
  useRealToken: boolean
  setUseRealToken: (useRealToken: boolean) => void
  resetSettings: () => void
}
```

### 存储策略
- **sessionStorage**: 设置仅在当前会话有效
- **自动序列化**: Zustand 自动处理 JSON 序列化
- **默认值**: useRealToken 默认为 false

### 组件集成
```typescript
// 在任何组件中使用
const { useRealToken, setUseRealToken } = useDocsStore()
```

## 📋 使用方式

### 1. TokenSettings 组件
- 自动从 store 获取当前状态
- 用户切换开关时直接更新 store
- 只有用户有 token 时才显示

### 2. ApiProcedureCard 组件
- 从 store 获取 useRealToken 状态
- 根据状态决定 curl 命令中是否使用真实 token
- 无需通过 props 传递状态

### 3. 状态同步
- 所有组件自动同步最新状态
- 用户在任何地方修改设置，所有 curl 命令立即更新
- 刷新页面后设置保持不变（sessionStorage）

## 🎨 优势特点

### 1. 简化架构
- **减少 Props**: 移除了多层 props 传递
- **集中管理**: 所有相关状态集中在一个 store
- **自动同步**: 组件间状态自动同步

### 2. 用户体验
- **即时生效**: 切换开关后所有 curl 命令立即更新
- **状态持久**: 设置在会话期间保持不变
- **智能显示**: 只有在有 token 时才显示开关

### 3. 开发体验
- **类型安全**: 完整的 TypeScript 类型支持
- **简单API**: 清晰简洁的 store API
- **易于扩展**: 可以轻松添加更多文档相关设置

## 🔄 数据流

### 之前的数据流
```
主页面 useState -> TokenSettings props -> 回调更新
主页面 state -> ApiTabs props -> ApiProcedureCard props
```

### 现在的数据流
```
TokenSettings -> useDocsStore -> 直接更新
ApiProcedureCard -> useDocsStore -> 直接获取
```

## 📝 代码示例

### Store 使用
```typescript
// 获取状态
const { useRealToken } = useDocsStore()

// 更新状态
const { setUseRealToken } = useDocsStore()
setUseRealToken(true)

// 重置设置
const { resetSettings } = useDocsStore()
resetSettings()
```

### 组件中使用
```typescript
export function TokenSettings({ hasToken }: TokenSettingsProps) {
  const { useRealToken, setUseRealToken } = useDocsStore()
  
  return (
    <Switch
      checked={useRealToken}
      onChange={(event) => setUseRealToken(event.currentTarget.checked)}
    />
  )
}
```

## 🚀 后续扩展

### 可能的扩展功能
1. **更多文档设置**: 添加其他文档相关的用户偏好
2. **主题设置**: 文档页面的主题偏好
3. **显示设置**: 参数表格的显示方式偏好
4. **历史记录**: 记录用户常用的接口

### 扩展示例
```typescript
interface DocsState {
  useRealToken: boolean
  showParameterDescriptions: boolean
  preferredTheme: 'light' | 'dark' | 'auto'
  favoriteEndpoints: string[]
  // ... 更多设置
}
```

这个 docs store 的实现大大简化了组件间的状态管理，提升了代码的可维护性和用户体验。通过集中管理文档相关的设置，为后续功能扩展奠定了良好的基础。
