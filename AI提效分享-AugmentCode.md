# AI代码编辑器：Augment Code 使用分享

## 背景

大家好！今天想跟大家分享一下我在AI辅助编程工具上的一些实战经验。当前AI编辑器和插件发展得特别快，像 Cursor 这样的 AI IDE 已经很普及了，能极大的提升开发效率。市面上类似的AI编码工具其实挺多的，主流的有两类形态：

- 插件形态：Augment Code、Cline、GitHub Copilot、工蜂 Copilot
- 独立应用：Cursor、Windsurf、Trae

插件形态的工具，通常会和现有的 IDE 如 VSCode、JetBrains 等集成；独立应用类的工具，通常会基于 VSCode 开源代码二次开发，并兼容 VSCode 的插件系统。

这两类工具能提供的功能差异并不大，目前最热门的还是 Cursor，不过 Augment Code 凭借其强大的上下文理解能力，也越来越被大家所关注。

我使用这些工具也有一段时间了，在深度使用后，我发现 Augment Code 在很多场景下，能提供相比 Cursor 更加智能的体验。接下来我讲简单介绍一下 Augment Code ，并与 Cursor 进行对比，希望能给大家一些参考。

## Augment Code 简单介绍

### 核心功能

Augment Code 的核心和功能其实和 Cursor 是类似的。

- Chat 模式：通过对话提问，得到代码解释或建议，但是不会主动修改代码。该模式也支持 MCP的调用。
- Agent 模式：一个自主工作的智能体。你给它一个明确的目标或任务，它会尝试独立分析、规划并执行，可能涉及多步骤操作，最终完成目标。这个会执行命令和修改代码。
- 代码补全、Next Edit：基于光标位置与上下文，智能生成下一步代码编辑建议，支持一键应用。类似于 Cursor 的 Tab、Jump To Tab 功能。

### 核心优势

- 强大的“Augment Context Engine 实时上下文引擎”，它能够全面理解大型代码库、文档和依赖项，并将其融入到每次按键操作的上下文感知中。这项能力解决了许多现有 AI 编码工具在处理大型代码库时面临的上下文限制问题。
- 更大的上下文窗口（200K），在这个基础上， Augment Code 在处理超大型代码库和复杂的、多步骤的任务时，表现出更强的能力。作为对比，Cursor的上下文限制是 10K。
- 优化的模型性能：预配置的 AI 模型（如 Claude Sonnet 4）经过优化，为编码任务提供高性能和高可靠性 。其在 SWE-bench 上的领先表现证明了这一点.
- 更好的开箱即用：默认的 Rules 就能很好的完成大部分任务，写出风格一致高质量的代码，而 Cursor 往往需要制定复杂的 Rules 约束其行为。

## Augment Code vs. Cursor

| 对比项     | Augment Code                   | Cursor                                                   |
| ---------- | ------------------------------ | -------------------------------------------------------- |
| 上下文窗口 | 200K                           | 10K                                                      |
| 上下文优秀 | 强大                           | 一般                                                     |
| 模型选择   | 自动选择模型（Claude、OpenAI） | 支持手动选择，支持 Claude, Gemini, OpenAI,支持自定义模型 |
| 价格       | 50$/月，600次请求              | 20$/月，500次快速请求，无限慢速请求                      |
| 免费计划   | 50次请求/月                    | 50次请求/月                                              |
| 自定义规则 | 支持                           | 支持                                                     |
| MCP        | 支持                           | 支持                                                     |
| 隐私与安全 | 免费计划可能导致用于模型训练   | 支持隐私模式                                             |
| 使用场景   | 中大型项目                     | 中小型项目                                               |
| 使用体验   | 流畅                           | 偶尔会出现降智、模型使用受限的情况                       |

## 总结

虽然 Augment Code 在部分场景下，比 Cursor 更好用，但总的来说，他们提供的的功能还是类似的。而且 AI 代码编辑器还在高速发展中，技术迭代速度惊人。当下选择好用、适合自己的工具很关键。结合这类工具，并总结出一套适合自己的使用方法，是一定能极大的提升开发效率的。
