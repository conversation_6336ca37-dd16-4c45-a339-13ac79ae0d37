import { z } from 'zod'
import {
  protectedProcedure,
  router,
  projectProcedure,
  projectWriteProcedure,
  projectOwnerProcedure,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { generateInterfacePath } from '../utils/interface-utils'

// 定义接口元数据输入验证
const interfaceMetadataSchema = z.object({
  protoFileId: z.number().describe('Proto文件ID'),
  serviceName: z.string().min(1, '服务名不能为空').describe('服务名称'),
  methodName: z.string().min(1, '方法名不能为空').describe('方法名称'),
  pathRuleId: z.number().optional().nullable().describe('路径规则ID（可选）'),
})

export const interfaceMetadataRouter = router({
  // ===== 接口元数据管理 =====
  // 这里可以添加接口元数据相关的操作
  // 例如：获取接口元数据、更新接口元数据、为接口分配标签等

  // 获取接口元数据
  getInterfaceMetadata: projectProcedure
    .meta({ description: '获取指定接口的元数据信息，包括标签和路径规则' })
    .input(
      z.object({
        protoFileId: z.number().describe('Proto文件ID'),
        serviceName: z.string().describe('服务名称'),
        methodName: z.string().describe('方法名称'),
      }),
    )
    .query(async ({ ctx: { project }, input }) => {
      const { protoFileId, serviceName, methodName } = input

      // 验证 protoFile 是否属于该项目
      const protoFile = await prisma.protoFile.findFirst({
        where: {
          id: protoFileId,
          projectId: project.id,
        },
      })

      if (!protoFile) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Proto 文件不存在或不属于当前项目',
        })
      }

      // 查找接口元数据
      const metadata = await prisma.interfaceMetadata.findUnique({
        where: {
          protoFileId_serviceName_methodName: {
            protoFileId,
            serviceName,
            methodName,
          },
        },
        include: {
          tags: true,
          pathRule: true,
        },
      })

      // 获取项目的默认路径规则
      const defaultPathRule = await prisma.pathRule.findFirst({
        where: {
          projectId: project.id,
          default: true,
        },
      })

      // 计算应用路径规则后的路径
      let path = null
      if (metadata?.pathRule) {
        // 使用接口自身的路径规则
        path = generateInterfacePath(metadata.pathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
      } else if (defaultPathRule) {
        // 使用项目默认路径规则
        path = generateInterfacePath(defaultPathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
      }

      // 如果元数据不存在，返回空数据
      if (!metadata) {
        return { metadata: null, defaultPathRule, tags: [], path: path ?? null }
      }

      return {
        metadata,
        defaultPathRule,
        tags: metadata.tags,
        path,
      }
    }),

  // 更新接口元数据
  updateInterfaceMetadata: projectWriteProcedure
    .meta({ description: '更新接口元数据，包括路径规则和标签关联' })
    .input(
      z.object({
        protoFileId: z.number().describe('Proto文件ID'),
        serviceName: z.string().describe('服务名称'),
        methodName: z.string().describe('方法名称'),
        pathRuleId: z.number().nullable().optional().describe('路径规则ID，null表示清除'),
        tagIds: z.array(z.number()).optional().describe('标签ID数组'),
      }),
    )
    .mutation(async ({ ctx: { project }, input }) => {
      const { protoFileId, serviceName, methodName, pathRuleId, tagIds } = input

      // 验证 protoFile 是否属于该项目
      const protoFile = await prisma.protoFile.findFirst({
        where: {
          id: protoFileId,
          projectId: project.id,
        },
      })

      if (!protoFile) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Proto 文件不存在或不属于当前项目',
        })
      }

      // 如果指定了路径规则，验证其是否属于该项目
      if (pathRuleId !== null && pathRuleId !== undefined) {
        const pathRule = await prisma.pathRule.findFirst({
          where: {
            id: pathRuleId,
            projectId: project.id,
          },
        })

        if (!pathRule) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '路径规则不存在或不属于当前项目',
          })
        }
      }

      // 查找或创建接口元数据
      let metadata = await prisma.interfaceMetadata.findUnique({
        where: {
          protoFileId_serviceName_methodName: {
            protoFileId,
            serviceName,
            methodName,
          },
        },
      })

      if (!metadata) {
        // 如果元数据不存在，创建新的元数据
        metadata = await prisma.interfaceMetadata.create({
          data: {
            protoFileId,
            serviceName,
            methodName,
            projectId: project.id,
            pathRuleId: pathRuleId === null ? null : pathRuleId,
          },
        })
      } else {
        // 如果元数据存在，更新路径规则
        metadata = await prisma.interfaceMetadata.update({
          where: { id: metadata.id },
          data: {
            pathRuleId: pathRuleId === null ? null : pathRuleId,
          },
        })
      }

      // 更新标签关联
      if (tagIds !== undefined) {
        // 断开所有现有标签连接
        await prisma.interfaceMetadata.update({
          where: { id: metadata.id },
          data: {
            tags: {
              set: [], // 清空所有标签
            },
          },
        })

        // 如果有新标签，重新建立连接
        if (tagIds.length > 0) {
          await prisma.interfaceMetadata.update({
            where: { id: metadata.id },
            data: {
              tags: {
                connect: tagIds.map(id => ({ id })),
              },
            },
          })
        }
      }

      // 获取更新后的元数据（包括标签）
      const updatedMetadata = await prisma.interfaceMetadata.findUnique({
        where: { id: metadata.id },
        include: {
          tags: true,
          pathRule: true,
        },
      })

      // 获取默认路径规则
      const defaultPathRule = await prisma.pathRule.findFirst({
        where: {
          projectId: project.id,
          default: true,
        },
      })

      // 计算应用路径规则后的路径
      let path = null
      if (updatedMetadata?.pathRule) {
        // 使用接口自身的路径规则
        path = generateInterfacePath(updatedMetadata.pathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
      } else if (defaultPathRule) {
        // 使用项目默认路径规则
        path = generateInterfacePath(defaultPathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
      }

      return {
        metadata: updatedMetadata,
        defaultPathRule,
        path,
      }
    }),

  // 获取接口路径
  getInterfacePath: projectProcedure
    .meta({ description: '获取接口的完整路径，基于路径规则生成' })
    .input(
      z.object({
        protoFileId: z.number().describe('Proto文件ID'),
        serviceName: z.string().describe('服务名称'),
        methodName: z.string().describe('方法名称'),
      }),
    )
    .query(async ({ ctx: { project }, input }) => {
      const { protoFileId, serviceName, methodName } = input

      // 验证 protoFile 是否属于该项目
      const protoFile = await prisma.protoFile.findFirst({
        where: {
          id: protoFileId,
          projectId: project.id,
        },
      })

      if (!protoFile) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Proto 文件不存在或不属于当前项目',
        })
      }

      // 查找接口元数据及其关联的路径规则
      const metadata = await prisma.interfaceMetadata.findUnique({
        where: {
          protoFileId_serviceName_methodName: {
            protoFileId,
            serviceName,
            methodName,
          },
        },
        include: {
          pathRule: true,
        },
      })

      // 如果接口有特定的路径规则，优先使用
      if (metadata?.pathRule) {
        const path = generateInterfacePath(metadata.pathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
        return {
          path,
          pathRule: metadata.pathRule,
          isDefault: false,
        }
      }

      // 如果接口没有特定的路径规则，尝试使用项目的默认规则
      const defaultPathRule = await prisma.pathRule.findFirst({
        where: {
          projectId: project.id,
          default: true,
        },
      })

      if (defaultPathRule) {
        const path = generateInterfacePath(defaultPathRule.pattern, {
          serviceName,
          methodName,
          protoFileName: protoFile.name,
          projectName: project.name,
        })
        return {
          path,
          pathRule: defaultPathRule,
          isDefault: true,
        }
      }

      // 如果没有任何规则，使用默认格式
      return {
        path: `/${serviceName}/${methodName}`,
        pathRule: null,
        isDefault: false,
      }
    }),

  // 获取项目所有标签
  getProjectTags: projectProcedure
    .meta({ description: '获取项目下的所有标签' })
    .query(async ({ ctx: { project } }) => {
      return await prisma.tag.findMany({
        where: { projectId: project.id },
        orderBy: { name: 'asc' },
      })
    }),

  // 获取项目所有路径规则
  getProjectPathRules: projectProcedure
    .meta({ description: '获取项目下的所有路径规则' })
    .query(async ({ ctx: { project } }) => {
      return await prisma.pathRule.findMany({
        where: { projectId: project.id },
        orderBy: { name: 'asc' },
      })
    }),
})
