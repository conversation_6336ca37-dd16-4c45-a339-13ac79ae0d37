import { z } from 'zod'
import { publicProcedure, protectedProcedure, router } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import axios from 'axios'
import { getGitUserAccessToken } from '../services/repository'
import { omit } from 'lodash-es'
import crypto from 'crypto'

export const userRouter = router({
  hello: publicProcedure.meta({ description: '简单的hello接口，用于测试连接' }).query(async () => {
    return 'hello'
  }),

  // 获取当前用户信息
  getCurrentUser: publicProcedure
    .meta({ description: '获取当前认证用户的信息' })
    .query(async ({ ctx }) => {
      const username = ctx.taiIdentity?.LoginName

      if (!username) {
        return null
      }

      // 查找用户，如果不存在则创建
      const user = await prisma.user.findUnique({
        where: { username },
        select: {
          username: true,
          chineseName: true,
          is_admin: true,
          gitAccessToken: true,
          deptId: true,
          deptName: true,
          staffId: true,
        },
      })

      return user ? { ...omit(user, ['gitAccessToken']), isBindGit: !!user.gitAccessToken } : null
    }),

  // 获取用户列表
  list: protectedProcedure.meta({ description: '获取系统中所有用户的列表' }).query(async () => {
    return await prisma.user.findMany({
      select: {
        username: true,
        chineseName: true,
        deptId: true,
        deptName: true,
        staffId: true,
      },
    })
  }),

  // 根据用户名获取用户
  getByUsername: publicProcedure
    .meta({ description: '根据用户名获取用户信息' })
    .input(z.object({ username: z.string().describe('要查找的用户名') }))
    .query(async ({ input }) => {
      const user = await prisma.user.findUnique({
        where: { username: input.username },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '用户不存在',
        })
      }

      return user
    }),

  // 删除用户
  delete: protectedProcedure
    .meta({ description: '删除用户账户（仅管理员或本人可操作）' })
    .input(z.object({ id: z.string().describe('要删除的用户名') }))
    .mutation(async ({ ctx, input }) => {
      // 只有管理员或用户本人可以删除用户
      if (!ctx.user.is_admin && ctx.user.username !== input.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '您没有权限删除此用户',
        })
      }

      return await prisma.user.delete({ where: { username: input.id } })
    }),

  setUserGitAccessToken: protectedProcedure
    .meta({ description: '使用授权码设置用户Git访问令牌' })
    .input(z.object({ code: z.string().describe('Git OAuth流程的授权码') }))
    .mutation(async ({ ctx, input }) => {
      const data = await getGitUserAccessToken(input.code)
      await prisma.user.update({
        where: { username: ctx.user.username },
        data: {
          gitAccessToken: data.access_token,
          gitRefreshToken: data.refresh_token,
          gitAccessTokenExpiresAt: new Date(Date.now() + data.expires_in * 1000),
        },
      })
      return {}
    }),

  // 解绑工蜂 Git 账号
  unbindGitAccount: protectedProcedure
    .meta({ description: '从用户配置文件中解绑Git账号' })
    .mutation(async ({ ctx }) => {
      // 检查用户是否已绑定工蜂 Git 账号
      const user = await prisma.user.findUnique({
        where: { username: ctx.user.username },
        select: { gitAccessToken: true },
      })

      if (!user?.gitAccessToken) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '您还未绑定工蜂 Git 账号',
        })
      }

      // 清除所有Git相关数据
      await prisma.user.update({
        where: { username: ctx.user.username },
        data: {
          gitAccessToken: null,
          gitRefreshToken: null,
          gitAccessTokenExpiresAt: null,
        },
      })

      return { success: true }
    }),

  // 生成API Token
  generateApiToken: protectedProcedure
    .meta({ description: '为当前用户生成新的API令牌' })
    .mutation(async ({ ctx }) => {
      // 生成随机token
      const token = crypto.randomBytes(32).toString('hex')

      // 更新用户的API token
      await prisma.user.update({
        where: { username: ctx.user.username },
        data: {
          apiToken: token,
          apiTokenCreatedAt: new Date(),
        },
      })

      return { token }
    }),

  // 获取当前用户的API Token信息
  getApiToken: protectedProcedure
    .meta({ description: '获取当前用户的API令牌信息' })
    .query(async ({ ctx }) => {
      const user = await prisma.user.findUnique({
        where: { username: ctx.user.username },
        select: {
          apiToken: true,
          apiTokenCreatedAt: true,
        },
      })

      return {
        hasToken: !!user?.apiToken,
        token: user?.apiToken || null,
        createdAt: user?.apiTokenCreatedAt,
      }
    }),

  // 删除API Token
  deleteApiToken: protectedProcedure
    .meta({ description: '删除当前用户的API令牌' })
    .mutation(async ({ ctx }) => {
      await prisma.user.update({
        where: { username: ctx.user.username },
        data: {
          apiToken: null,
          apiTokenCreatedAt: null,
        },
      })

      return { success: true }
    }),
})
