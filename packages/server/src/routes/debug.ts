import { z } from 'zod'
import {
  projectProcedure,
  projectWriteProcedure,
  router,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import axios, { AxiosError } from 'axios'

// 调试请求的schema
const debugRequestSchema = z.object({
  protoFileId: z.number().describe('Proto文件ID'),
  serviceName: z.string().describe('服务名'),
  methodName: z.string().describe('方法名'),
  environmentId: z.number().describe('环境ID'),
  requestParams: z.record(z.any()).describe('请求参数'),
  saveCaseName: z.string().optional().describe('保存为用例的名称（可选）'),
})

export const debugRouter = router({
  // 执行接口调试
  execute: projectWriteProcedure
    .meta({ description: '执行接口调试请求' })
    .input(debugRequestSchema)
    .mutation(async ({ input, ctx }) => {
      const startTime = Date.now()

      // 验证Proto文件是否属于当前项目
      const protoFile = await prisma.protoFile.findFirst({
        where: {
          id: input.protoFileId,
          projectId: ctx.project.id,
        },
      })

      if (!protoFile) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Proto文件不存在或不属于当前项目',
        })
      }

      // 获取项目环境配置
      const binding = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
        include: {
          environment: true,
        },
      })

      if (!binding) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '项目未绑定此环境',
        })
      }

      // 合并配置
      const mergedHeaders = {
        'Content-Type': 'application/json',
        ...(binding.environment.headers as Record<string, string> || {}),
        ...(binding.customHeaders as Record<string, string> || {}),
      }

      const mergedCookies = {
        ...(binding.environment.cookies as Record<string, string> || {}),
        ...(binding.customCookies as Record<string, string> || {}),
      }

      // 构建Cookie字符串
      const cookieString = Object.entries(mergedCookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ')

      if (cookieString) {
        mergedHeaders['Cookie'] = cookieString
      }

      // 构建请求URL（这里需要根据实际的Proto服务路径规则来构建）
      const requestUrl = `${binding.environment.baseUrl}/${input.serviceName}/${input.methodName}`
      const requestMethod = 'POST' // 假设都是POST请求，可以根据需要调整

      let responseStatus: number | null = null
      let responseHeaders: Record<string, any> | null = null
      let responseBody: any = null
      let errorMessage: string | null = null

      try {
        // 发送HTTP请求
        const response = await axios({
          method: requestMethod,
          url: requestUrl,
          headers: mergedHeaders,
          data: input.requestParams,
          timeout: 30000, // 30秒超时
          validateStatus: () => true, // 不抛出状态码错误，我们手动处理
        })

        responseStatus = response.status
        responseHeaders = response.headers
        responseBody = response.data

      } catch (error) {
        if (error instanceof AxiosError) {
          responseStatus = error.response?.status || null
          responseHeaders = error.response?.headers || null
          responseBody = error.response?.data || null
          errorMessage = error.message
        } else {
          errorMessage = error instanceof Error ? error.message : '未知错误'
        }
      }

      const responseTime = Date.now() - startTime

      // 保存调试历史
      const debugHistory = await prisma.debugHistory.create({
        data: {
          projectId: ctx.project.id,
          protoFileId: input.protoFileId,
          serviceName: input.serviceName,
          methodName: input.methodName,
          environmentId: input.environmentId,
          requestUrl,
          requestMethod,
          requestHeaders: mergedHeaders,
          requestParams: input.requestParams,
          responseStatus,
          responseHeaders,
          responseBody,
          responseTime,
          errorMessage,
          executedBy: ctx.user.username,
        },
      })

      // 如果指定了用例名称，保存为用例
      let savedCase = null
      if (input.saveCaseName) {
        try {
          savedCase = await prisma.debugCase.create({
            data: {
              name: input.saveCaseName,
              projectId: ctx.project.id,
              protoFileId: input.protoFileId,
              serviceName: input.serviceName,
              methodName: input.methodName,
              environmentId: input.environmentId,
              requestParams: input.requestParams,
              expectedResult: responseStatus && responseStatus < 400 ? responseBody : null,
              createdBy: ctx.user.username,
            },
          })
        } catch (error) {
          // 用例保存失败不影响调试结果
          console.error('保存调试用例失败:', error)
        }
      }

      return {
        debugHistory,
        savedCase,
        result: {
          requestUrl,
          requestMethod,
          requestHeaders: mergedHeaders,
          requestParams: input.requestParams,
          responseStatus,
          responseHeaders,
          responseBody,
          responseTime,
          errorMessage,
          success: responseStatus !== null && responseStatus >= 200 && responseStatus < 400,
        },
      }
    }),

  // 获取接口调试历史
  getHistory: projectProcedure
    .meta({ description: '获取接口调试历史记录' })
    .input(
      z.object({
        protoFileId: z.number().describe('Proto文件ID'),
        serviceName: z.string().describe('服务名'),
        methodName: z.string().describe('方法名'),
        environmentId: z.number().optional().describe('环境ID（可选）'),
        limit: z.number().default(20).describe('返回记录数量限制'),
        offset: z.number().default(0).describe('偏移量'),
      })
    )
    .query(async ({ input, ctx }) => {
      const where = {
        projectId: ctx.project.id,
        protoFileId: input.protoFileId,
        serviceName: input.serviceName,
        methodName: input.methodName,
        ...(input.environmentId && { environmentId: input.environmentId }),
      }

      const [histories, total] = await Promise.all([
        prisma.debugHistory.findMany({
          where,
          include: {
            environment: {
              select: { id: true, name: true },
            },
            executedByUser: {
              select: { username: true, chineseName: true },
            },
            debugCase: {
              select: { id: true, name: true },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        prisma.debugHistory.count({ where }),
      ])

      return {
        histories,
        total,
        hasMore: input.offset + input.limit < total,
      }
    }),

  // 重新执行历史记录
  reExecute: projectWriteProcedure
    .meta({ description: '重新执行历史调试记录' })
    .input(
      z.object({
        historyId: z.number().describe('历史记录ID'),
        saveCaseName: z.string().optional().describe('保存为用例的名称（可选）'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      // 获取历史记录
      const history = await prisma.debugHistory.findFirst({
        where: {
          id: input.historyId,
          projectId: ctx.project.id,
        },
      })

      if (!history) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试历史记录不存在',
        })
      }

      // 重新执行调试
      return await debugRouter
        .createCaller(ctx)
        .execute({
          protoFileId: history.protoFileId,
          serviceName: history.serviceName,
          methodName: history.methodName,
          environmentId: history.environmentId,
          requestParams: history.requestParams as Record<string, any>,
          saveCaseName: input.saveCaseName,
        })
    }),
})
