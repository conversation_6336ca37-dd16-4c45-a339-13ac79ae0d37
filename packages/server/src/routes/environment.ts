import { z } from 'zod'
import {
  protectedProcedure,
  router,
  projectProcedure,
  projectWriteProcedure,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

// 环境配置验证schema
const environmentSchema = z.object({
  name: z.string().min(1, '环境名称不能为空').describe('环境名称'),
  description: z.string().optional().describe('环境描述'),
  baseUrl: z.string().url('请输入有效的URL').describe('基础URL'),
  headers: z.record(z.string()).optional().describe('默认请求头'),
  cookies: z.record(z.string()).optional().describe('Cookie配置'),
  admins: z.array(z.string()).optional().describe('管理员用户名列表'),
  users: z.array(z.string()).optional().describe('使用者用户名列表，空表示公开'),
})

// 检查用户是否有环境权限
const checkEnvironmentPermission = async (
  environmentId: number,
  username: string,
  requireAdmin = false
) => {
  const environment = await prisma.environment.findUnique({
    where: { id: environmentId },
  })

  if (!environment) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '环境不存在' })
  }

  // 创建者拥有所有权限
  if (environment.createdBy === username) {
    return environment
  }

  const admins = (environment.admins as string[]) || []
  const users = (environment.users as string[]) || null

  // 检查管理员权限
  if (requireAdmin) {
    if (!admins.includes(username)) {
      throw new TRPCError({ code: 'FORBIDDEN', message: '您没有管理此环境的权限' })
    }
  } else {
    // 检查使用权限
    const isAdmin = admins.includes(username)
    const isUser = users === null || users.includes(username) // null表示公开
    
    if (!isAdmin && !isUser) {
      throw new TRPCError({ code: 'FORBIDDEN', message: '您没有访问此环境的权限' })
    }
  }

  return environment
}

export const environmentRouter = router({
  // 获取所有环境列表（用户有权限访问的）
  list: protectedProcedure
    .meta({ description: '获取用户有权限访问的环境列表' })
    .query(async ({ ctx }) => {
      const environments = await prisma.environment.findMany({
        include: {
          createdByUser: {
            select: { username: true, chineseName: true },
          },
          _count: {
            select: {
              projectEnvironments: true,
              debugCases: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })

      // 过滤用户有权限访问的环境
      return environments.filter(env => {
        const admins = (env.admins as string[]) || []
        const users = (env.users as string[]) || null
        
        return (
          env.createdBy === ctx.user.username ||
          admins.includes(ctx.user.username) ||
          users === null ||
          users.includes(ctx.user.username)
        )
      }).map(env => ({
        ...env,
        // 判断用户权限级别
        permission: env.createdBy === ctx.user.username 
          ? 'owner' 
          : ((env.admins as string[]) || []).includes(ctx.user.username)
          ? 'admin'
          : 'user',
      }))
    }),

  // 创建环境
  create: protectedProcedure
    .meta({ description: '创建新的调试环境' })
    .input(environmentSchema)
    .mutation(async ({ input, ctx }) => {
      // 检查环境名称是否已存在
      const existing = await prisma.environment.findUnique({
        where: { name: input.name },
      })

      if (existing) {
        throw new TRPCError({ code: 'CONFLICT', message: '环境名称已存在' })
      }

      return await prisma.environment.create({
        data: {
          ...input,
          createdBy: ctx.user.username,
          headers: input.headers || {},
          cookies: input.cookies || {},
          admins: input.admins || [],
          users: input.users,
        },
        include: {
          createdByUser: {
            select: { username: true, chineseName: true },
          },
        },
      })
    }),

  // 获取环境详情
  getById: protectedProcedure
    .meta({ description: '获取环境详细信息' })
    .input(z.object({ id: z.number().describe('环境ID') }))
    .query(async ({ input, ctx }) => {
      const environment = await checkEnvironmentPermission(input.id, ctx.user.username)
      
      return await prisma.environment.findUnique({
        where: { id: input.id },
        include: {
          createdByUser: {
            select: { username: true, chineseName: true },
          },
          projectEnvironments: {
            include: {
              project: {
                select: { id: true, name: true, repository: true },
              },
            },
          },
          _count: {
            select: {
              debugCases: true,
              debugHistories: true,
            },
          },
        },
      })
    }),

  // 更新环境
  update: protectedProcedure
    .meta({ description: '更新环境配置' })
    .input(
      z.object({
        id: z.number().describe('环境ID'),
        data: environmentSchema.partial(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      await checkEnvironmentPermission(input.id, ctx.user.username, true)

      // 如果更新名称，检查是否冲突
      if (input.data.name) {
        const existing = await prisma.environment.findFirst({
          where: {
            name: input.data.name,
            id: { not: input.id },
          },
        })

        if (existing) {
          throw new TRPCError({ code: 'CONFLICT', message: '环境名称已存在' })
        }
      }

      return await prisma.environment.update({
        where: { id: input.id },
        data: input.data,
        include: {
          createdByUser: {
            select: { username: true, chineseName: true },
          },
        },
      })
    }),

  // 删除环境
  delete: protectedProcedure
    .meta({ description: '删除环境' })
    .input(z.object({ id: z.number().describe('环境ID') }))
    .mutation(async ({ input, ctx }) => {
      const environment = await checkEnvironmentPermission(input.id, ctx.user.username, true)

      // 只有创建者可以删除
      if (environment.createdBy !== ctx.user.username) {
        throw new TRPCError({ code: 'FORBIDDEN', message: '只有创建者可以删除环境' })
      }

      await prisma.environment.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // 更新环境Cookie（用于登录后自动保存）
  updateCookies: protectedProcedure
    .meta({ description: '更新环境Cookie配置' })
    .input(
      z.object({
        id: z.number().describe('环境ID'),
        cookies: z.record(z.string()).describe('Cookie配置'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      await checkEnvironmentPermission(input.id, ctx.user.username)

      return await prisma.environment.update({
        where: { id: input.id },
        data: { cookies: input.cookies },
      })
    }),

  // 获取项目可用的环境列表
  getAvailableForProject: projectProcedure
    .meta({ description: '获取项目可用的环境列表' })
    .query(async ({ ctx }) => {
      const environments = await prisma.environment.findMany({
        include: {
          projectEnvironments: {
            where: { projectId: ctx.project.id },
          },
        },
        orderBy: { name: 'asc' },
      })

      // 过滤用户有权限访问的环境
      return environments.filter(env => {
        const admins = (env.admins as string[]) || []
        const users = (env.users as string[]) || null
        
        return (
          env.createdBy === ctx.user.username ||
          admins.includes(ctx.user.username) ||
          users === null ||
          users.includes(ctx.user.username)
        )
      }).map(env => ({
        ...env,
        isBound: env.projectEnvironments.length > 0,
      }))
    }),
})
