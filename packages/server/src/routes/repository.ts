import { z } from 'zod'
import { router, adminProcedure } from '../trpc'
import { GitService } from '../services/repository'

export const repositoryRouter = router({
  // list: protectedProcedure.query(async () => {
  //   const repositories = await prisma.repository.findMany({
  //     include: { projects: true },
  //   })
  //   return repositories
  // }),

  // /** 按关键词搜索所有可用的仓库 */
  // searchAvailableRepositories: protectedProcedure
  //   .input(z.object({ keyword: z.string().min(1) }))
  //   .query(async ({ input, ctx }) => {
  //     const gitService = new GitService(ctx.user)
  //     return await gitService.searchProjectsInGroup('rick_proto', input.keyword)
  //   }),

  // 获取项目组下所有项目
  getAvailableRepositories: adminProcedure
    .meta({ description: '获取项目组中所有可用的仓库（仅管理员）' })
    .query(async ({ ctx }) => {
      const gitService = new GitService(ctx.user)
      return await gitService.getProjectsInGroup('rick_proto')
    }),

  getRepositoryAvailableProjects: adminProcedure
    .meta({ description: '获取指定仓库中所有可用的项目/模块' })
    .input(z.object({ repository: z.string().describe('要获取项目的仓库名称') }))
    .query(async ({ input, ctx }) => {
      const gitService = new GitService(ctx.user)
      const { repository } = input
      return await gitService.getRepositoryModules(repository)
    }),
})
