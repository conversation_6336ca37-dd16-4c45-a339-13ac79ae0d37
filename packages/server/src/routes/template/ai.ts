import { z } from 'zod'
import { projectProcedure, protectedProcedure, router, t } from '../../trpc'
import { prisma } from '../../prisma'
import { TRPCError } from '@trpc/server'
import { OpenAI } from 'openai'
import { tracked } from '@trpc/server'
import { protoRouter } from '../proto'
import {
  getServiceMethodContent,
  mergeParsedProtoJSON,
  ParsedProtoJSON,
} from '../../services/parser'
import { safeJsonParse } from '../../utils/common'
import { modelChatCompletions } from '../../services/chat'

// 模板可见性枚举
const TemplateVisibilityEnum = z.enum(['PUBLIC', 'PRIVATE', 'OPEN'])

export const aiTemplateRouter = router({
  listAll: protectedProcedure
    .meta({ description: '获取用户可访问的所有AI模板列表' })
    .query(async ({ ctx }) => {
      const username = ctx.user.username
      return await prisma.aiTemplate.findMany({
        where: {
          OR: [
            { createdBy: username }, // 用户创建的模板
            { auth: 'PUBLIC' }, // 公共模板
            { auth: 'OPEN' }, // 开放模板
          ],
        },
      })
    }),

  // 列出用户可访问的 AI 模板
  listByProject: projectProcedure
    .meta({ description: '获取指定项目绑定的AI模板列表' })
    .query(async ({ ctx, input }) => {
      const projectId = input.projectId
      return await prisma.aiTemplate.findMany({
        where: {
          projects: { some: { id: projectId } },
          OR: [
            { createdBy: ctx.user.username }, // 用户创建的模板
            { auth: 'PUBLIC' }, // 公共模板
            { auth: 'OPEN' }, // 开放模板
          ],
        },
      })
    }),

  // 获取模板详情
  getById: protectedProcedure
    .meta({ description: '根据ID获取AI模板详情' })
    .input(
      z.object({
        id: z.number().describe('AI模板ID'),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await prisma.aiTemplate.findFirst({
        where: {
          OR: [
            { createdBy: ctx.user.username }, // 用户创建的模板
            { auth: 'PUBLIC' }, // 公共模板
            { auth: 'OPEN' }, // 开放模板
          ],
          id: input.id,
        },
      })
      if (!data) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在或者您没有权限访问' })
      }
      return data
    }),

  // 创建 AI 模板
  create: protectedProcedure
    .meta({ description: '创建新的AI模板' })
    .input(
      z.object({
        prompt: z.string().min(1).describe('AI提示词内容'),
        auth: TemplateVisibilityEnum.describe('模板可见性（PUBLIC/PRIVATE/OPEN）'),
        title: z.string().min(1).describe('模板标题'),
        desc: z.string().optional().describe('模板描述（可选）'),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username
      const { prompt, auth, title, desc } = input

      // 创建模板
      return await prisma.aiTemplate.create({
        data: { prompt, auth, createdBy: username, title, desc: desc || '' },
      })
    }),

  // 更新 AI 模板
  update: protectedProcedure
    .meta({ description: '更新AI模板信息' })
    .input(
      z.object({
        id: z.number().describe('AI模板ID'),
        prompt: z.string().min(1).optional().describe('AI提示词内容'),
        auth: TemplateVisibilityEnum.optional().describe('模板可见性'),
        title: z.string().min(1).optional().describe('模板标题'),
        desc: z.string().optional().describe('模板描述'),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username
      const { id, prompt, auth, title, desc } = input

      // 获取模板详情，检查是否是创建者
      const template = await prisma.aiTemplate.findUnique({
        where: { id },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth !== 'OPEN') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限更新此模板' })
      }

      // 更新模板
      return await prisma.aiTemplate.update({
        where: { id },
        data: {
          ...(prompt && { prompt }),
          ...(auth && { auth }),
          ...(title && { title }),
          ...(desc !== undefined && { desc }),
        },
      })
    }),

  getBoundProjects: protectedProcedure
    .meta({ description: '获取AI模板绑定的项目列表' })
    .input(z.object({ templateId: z.number().describe('AI模板ID') }))
    .query(async ({ ctx, input }) => {
      const { templateId } = input
      const info = await prisma.aiTemplate.findUnique({
        where: { id: templateId },
        include: {
          projects: {
            where: { members: { some: { username: ctx.user.username } } },
          },
        },
      })
      return info?.projects || []
    }),

  bindProject: projectProcedure
    .meta({ description: '将AI模板绑定到项目' })
    .input(z.object({ templateIds: z.array(z.number()).min(1).describe('要绑定的AI模板ID数组') }))
    .mutation(async ({ ctx, input }) => {
      const { templateIds } = input
      const projectId = ctx.project.id
      const username = ctx.user.username

      // 检查模板是否存在以及权限
      const templates = await prisma.aiTemplate.findMany({
        where: { id: { in: templateIds } },
      })

      // 检查是否所有请求的模板都存在
      if (templates.length !== templateIds.length) {
        const foundIds = templates.map(t => t.id)
        const missingIds = templateIds.filter(id => !foundIds.includes(id))
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `以下模板不存在: ${missingIds.join(', ')}`,
        })
      }

      // 检查权限：PRIVATE 模板只能由创建者绑定
      const privateTemplates = templates.filter(
        t => t.auth === 'PRIVATE' && t.createdBy !== username,
      )
      if (privateTemplates.length > 0) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: `您没有权限绑定以下私有模板: ${privateTemplates.map(t => t.id).join(', ')}`,
        })
      }

      // 过滤出尚未绑定的模板
      const existingTemplateIds = await prisma.aiTemplate.findMany({
        where: { projects: { some: { id: projectId } } },
      })
      const newTemplateIds = templateIds.filter(id => !existingTemplateIds.some(t => t.id === id))

      // 执行绑定操作
      await prisma.project.update({
        where: { id: projectId },
        data: {
          aiTemplates: { connect: newTemplateIds.map(id => ({ id })) },
        },
      })

      return { success: true }
    }),

  unbindProject: projectProcedure
    .meta({ description: '将AI模板从项目解绑' })
    .input(z.object({ templateIds: z.array(z.number()).min(1).describe('要解绑的AI模板ID数组') }))
    .mutation(async ({ ctx, input }) => {
      const { templateIds } = input
      const projectId = ctx.project.id

      // 检查要解绑的模板是否存在于当前项目中
      const existingTemplates = await prisma.aiTemplate.findMany({
        where: { id: { in: templateIds }, projects: { some: { id: projectId } } },
      })

      // 执行解绑操作
      await prisma.project.update({
        where: { id: projectId },
        data: {
          aiTemplates: {
            disconnect: existingTemplates.map(template => ({ id: template.id })),
          },
        },
      })

      return { success: true }
    }),

  // 删除 AI 模板
  delete: protectedProcedure
    .meta({ description: '删除AI模板' })
    .input(
      z.object({
        id: z.number().describe('要删除的AI模板ID'),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const username = ctx.user.username

      // 获取模板详情，检查是否是创建者
      const template = await prisma.aiTemplate.findUnique({
        where: { id: input.id },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth !== 'OPEN') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限删除此模板' })
      }

      // 删除模板
      return await prisma.aiTemplate.delete({
        where: { id: input.id },
      })
    }),

  // 执行 AI 模板（流式响应）
  execute: projectProcedure
    .meta({ description: '执行AI模板生成代码（流式响应）' })
    .input(
      z.object({
        id: z.number().describe('AI模板ID'),
        serviceName: z.string().describe('服务名称'),
        methodName: z.string().describe('方法名称'),
      }),
    )
    .subscription(async function* ({ ctx, input }) {
      const { methodName, serviceName, projectId, id: templateId } = input
      const username = ctx.user.username

      // 获取模板详情，检查是否是创建者
      const template = await prisma.aiTemplate.findUnique({
        where: { id: templateId },
      })

      if (!template) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '模板不存在' })
      }

      // 只有创建者或项目创建者可以更新模板
      if (template.createdBy !== username && template.auth === 'PRIVATE') {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有权限访问此模板' })
      }

      const start = Date.now()

      // 直接调用trpc 的方法
      const caller = t.createCallerFactory(protoRouter)(ctx)
      const protoData = await caller.getParsedContentByProject({ projectId })
      console.log(`获取proto数据耗时: ${Date.now() - start}ms`)
      const protoInfo = mergeParsedProtoJSON(
        [
          ...protoData.list.map(i => safeJsonParse<ParsedProtoJSON>(i.json)),
          ...protoData.extra.map(i => safeJsonParse<ParsedProtoJSON>(i.json)),
        ].filter(i => i !== null),
      )

      const apiInfo = getServiceMethodContent(
        protoInfo,
        `trpc.${ctx.project.repository}.${ctx.project.name}`,
        serviceName,
        methodName,
      )

      const interfaceMetaData = await prisma.interfaceMetadata.findFirst({
        where: { projectId, serviceName, methodName },
        include: { tags: true, pathRule: true },
      })

      const data = { ...apiInfo, interfaceMetaData }

      console.log(`解析proto数据耗时: ${Date.now() - start}ms`)

      try {
        const prompt = `${template.prompt}\n\n以下是API数据:\n${JSON.stringify(data)}\n仅输入代码，不要输出任何解释`

        let contentIndex = 0

        const messages = [
          { role: 'system' as const, content: '你是一个 API 文档助手' },
          { role: 'user' as const, content: prompt },
        ]

        for await (const content of modelChatCompletions({ messages })) {
          contentIndex++
          yield tracked(contentIndex.toString(), content)
        }

        console.log(`AI模板(ID: ${templateId})执行完成，共生成${contentIndex}个内容片段`)
      } catch (error) {
        const err = error as any
        const message: string = err?.error?.message ?? err?.message ?? 'AI 模板执行失败'
        yield tracked('error', message)
        console.error('AI 模板执行失败:', error)
      }
    }),
})
