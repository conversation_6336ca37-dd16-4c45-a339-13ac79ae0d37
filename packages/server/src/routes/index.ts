import { router } from '../trpc'
import { userRouter } from './user'
import { protoRouter } from './proto'
import { projectRouter } from './project'
import { templateRouter } from './template/'
import { repositoryRouter } from './repository'
import { interfaceMetadataRouter } from './interface-metadata'
import { tagRouter } from './tag.router'
import { pathRuleRouter } from './path-rule.router'
import { environmentRouter } from './environment'
import { projectEnvironmentRouter } from './project-environment'
import { debugRouter } from './debug'
import { debugCaseRouter } from './debug-case'

export const appRouter = router({
  user: userRouter,
  project: projectRouter,
  template: templateRouter,
  repository: repositoryRouter,
  proto: protoRouter,
  interfaceMetadata: interfaceMetadataRouter,
  tag: tagRouter,
  pathRule: pathRuleRouter,
  environment: environmentRouter,
  projectEnvironment: projectEnvironmentRouter,
  debug: debugRouter,
  debugCase: debugCaseRouter,
})

export type AppRouter = typeof appRouter
