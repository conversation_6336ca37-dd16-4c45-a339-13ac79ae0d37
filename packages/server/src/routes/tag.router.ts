import { z } from 'zod'
import { protectedProcedure, router, projectProcedure, projectWriteProcedure } from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

// 定义标签输入验证
const tagSchema = z.object({
  name: z.string().min(1, '标签名称不能为空').describe('标签名称'),
  color: z.string().optional().describe('标签颜色（可选）'),
  icon: z.string().optional().describe('标签图标（可选）'),
})

export const tagRouter = router({
  // 获取项目下所有标签
  getTags: projectProcedure
    .meta({ description: '获取项目下的所有标签' })
    .query(async ({ ctx: { project } }) => {
      return await prisma.tag.findMany({
        where: { projectId: project.id },
        orderBy: { name: 'asc' },
      })
    }),

  // 创建标签
  createTag: projectWriteProcedure
    .meta({ description: '为项目创建新标签' })
    .input(tagSchema)
    .mutation(async ({ ctx: { project }, input }) => {
      const { name, color, icon } = input

      // 检查同名标签是否已存在
      const existingTag = await prisma.tag.findUnique({
        where: {
          projectId_name: {
            projectId: project.id,
            name,
          },
        },
      })

      if (existingTag) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `标签 "${name}" 已存在`,
        })
      }

      // 创建新标签
      return await prisma.tag.create({
        data: {
          name,
          color,
          icon,
          projectId: project.id,
        },
      })
    }),

  // 更新标签
  updateTag: projectWriteProcedure
    .meta({ description: '更新现有标签' })
    .input(
      z.object({
        id: z.number().describe('要更新的标签ID'),
        ...tagSchema.shape,
      }),
    )
    .mutation(async ({ ctx: { project }, input }) => {
      const { id, name, color, icon } = input

      // 检查标签是否存在
      const existingTag = await prisma.tag.findUnique({
        where: { id },
      })

      if (!existingTag || existingTag.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '标签不存在或不属于当前项目',
        })
      }

      // 如果更改了名称，检查新名称是否已存在
      if (name !== existingTag.name) {
        const duplicateTag = await prisma.tag.findUnique({
          where: {
            projectId_name: {
              projectId: project.id,
              name,
            },
          },
        })

        if (duplicateTag) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `标签 "${name}" 已存在`,
          })
        }
      }

      // 更新标签
      return await prisma.tag.update({
        where: { id },
        data: {
          name,
          color,
          icon,
        },
      })
    }),

  // 删除标签
  deleteTag: projectWriteProcedure
    .meta({ description: '从项目中删除标签' })
    .input(z.object({ id: z.number().describe('要删除的标签ID') }))
    .mutation(async ({ ctx: { project }, input }) => {
      const { id } = input

      // 检查标签是否存在
      const existingTag = await prisma.tag.findUnique({
        where: { id },
      })

      if (!existingTag || existingTag.projectId !== project.id) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '标签不存在或不属于当前项目',
        })
      }

      // 删除标签 (关联的 InterfaceTagAssignment 会自动删除)
      await prisma.tag.delete({
        where: { id },
      })

      return { success: true }
    }),
})
