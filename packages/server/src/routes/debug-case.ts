import { z } from 'zod'
import {
  projectProcedure,
  projectWriteProcedure,
  router,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

// 调试用例schema
const debugCaseSchema = z.object({
  name: z.string().min(1, '用例名称不能为空').describe('用例名称'),
  description: z.string().optional().describe('用例描述'),
  protoFileId: z.number().describe('Proto文件ID'),
  serviceName: z.string().describe('服务名'),
  methodName: z.string().describe('方法名'),
  environmentId: z.number().describe('环境ID'),
  requestParams: z.record(z.any()).describe('请求参数'),
  expectedResult: z.record(z.any()).optional().describe('期望结果'),
})

export const debugCaseRouter = router({
  // 获取接口的调试用例列表
  list: projectProcedure
    .meta({ description: '获取接口的调试用例列表' })
    .input(
      z.object({
        protoFileId: z.number().describe('Proto文件ID'),
        serviceName: z.string().describe('服务名'),
        methodName: z.string().describe('方法名'),
        environmentId: z.number().optional().describe('环境ID（可选）'),
      })
    )
    .query(async ({ input, ctx }) => {
      const where = {
        projectId: ctx.project.id,
        protoFileId: input.protoFileId,
        serviceName: input.serviceName,
        methodName: input.methodName,
        ...(input.environmentId && { environmentId: input.environmentId }),
      }

      return await prisma.debugCase.findMany({
        where,
        include: {
          environment: {
            select: { id: true, name: true },
          },
          createdByUser: {
            select: { username: true, chineseName: true },
          },
          _count: {
            select: { debugHistories: true },
          },
        },
        orderBy: { createdAt: 'desc' },
      })
    }),

  // 创建调试用例
  create: projectWriteProcedure
    .meta({ description: '创建调试用例' })
    .input(debugCaseSchema)
    .mutation(async ({ input, ctx }) => {
      // 验证Proto文件是否属于当前项目
      const protoFile = await prisma.protoFile.findFirst({
        where: {
          id: input.protoFileId,
          projectId: ctx.project.id,
        },
      })

      if (!protoFile) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Proto文件不存在或不属于当前项目',
        })
      }

      // 验证环境是否绑定到项目
      const binding = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
      })

      if (!binding) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '项目未绑定此环境',
        })
      }

      return await prisma.debugCase.create({
        data: {
          ...input,
          projectId: ctx.project.id,
          createdBy: ctx.user.username,
        },
        include: {
          environment: {
            select: { id: true, name: true },
          },
          createdByUser: {
            select: { username: true, chineseName: true },
          },
        },
      })
    }),

  // 获取用例详情
  getById: projectProcedure
    .meta({ description: '获取调试用例详情' })
    .input(z.object({ id: z.number().describe('用例ID') }))
    .query(async ({ input, ctx }) => {
      const debugCase = await prisma.debugCase.findFirst({
        where: {
          id: input.id,
          projectId: ctx.project.id,
        },
        include: {
          environment: true,
          protoFile: {
            select: { id: true, name: true },
          },
          createdByUser: {
            select: { username: true, chineseName: true },
          },
          debugHistories: {
            include: {
              executedByUser: {
                select: { username: true, chineseName: true },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10, // 最近10次执行记录
          },
        },
      })

      if (!debugCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试用例不存在',
        })
      }

      return debugCase
    }),

  // 更新调试用例
  update: projectWriteProcedure
    .meta({ description: '更新调试用例' })
    .input(
      z.object({
        id: z.number().describe('用例ID'),
        data: debugCaseSchema.partial().omit({ protoFileId: true, serviceName: true, methodName: true }),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const debugCase = await prisma.debugCase.findFirst({
        where: {
          id: input.id,
          projectId: ctx.project.id,
        },
      })

      if (!debugCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试用例不存在',
        })
      }

      // 检查权限：只有创建者可以修改
      if (debugCase.createdBy !== ctx.user.username) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '只有创建者可以修改用例',
        })
      }

      return await prisma.debugCase.update({
        where: { id: input.id },
        data: input.data,
        include: {
          environment: {
            select: { id: true, name: true },
          },
          createdByUser: {
            select: { username: true, chineseName: true },
          },
        },
      })
    }),

  // 删除调试用例
  delete: projectWriteProcedure
    .meta({ description: '删除调试用例' })
    .input(z.object({ id: z.number().describe('用例ID') }))
    .mutation(async ({ input, ctx }) => {
      const debugCase = await prisma.debugCase.findFirst({
        where: {
          id: input.id,
          projectId: ctx.project.id,
        },
      })

      if (!debugCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试用例不存在',
        })
      }

      // 检查权限：只有创建者可以删除
      if (debugCase.createdBy !== ctx.user.username) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '只有创建者可以删除用例',
        })
      }

      await prisma.debugCase.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // 执行调试用例
  execute: projectWriteProcedure
    .meta({ description: '执行调试用例' })
    .input(z.object({ id: z.number().describe('用例ID') }))
    .mutation(async ({ input, ctx }) => {
      const debugCase = await prisma.debugCase.findFirst({
        where: {
          id: input.id,
          projectId: ctx.project.id,
        },
      })

      if (!debugCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试用例不存在',
        })
      }

      // 导入调试路由来执行
      const { debugRouter } = await import('./debug')
      
      const result = await debugRouter
        .createCaller(ctx)
        .execute({
          protoFileId: debugCase.protoFileId,
          serviceName: debugCase.serviceName,
          methodName: debugCase.methodName,
          environmentId: debugCase.environmentId,
          requestParams: debugCase.requestParams as Record<string, any>,
        })

      // 更新调试历史记录，关联到用例
      await prisma.debugHistory.update({
        where: { id: result.debugHistory.id },
        data: { debugCaseId: debugCase.id },
      })

      return {
        ...result,
        case: debugCase,
      }
    }),

  // 复制用例
  duplicate: projectWriteProcedure
    .meta({ description: '复制调试用例' })
    .input(
      z.object({
        id: z.number().describe('用例ID'),
        newName: z.string().min(1, '新用例名称不能为空').describe('新用例名称'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const originalCase = await prisma.debugCase.findFirst({
        where: {
          id: input.id,
          projectId: ctx.project.id,
        },
      })

      if (!originalCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '调试用例不存在',
        })
      }

      return await prisma.debugCase.create({
        data: {
          name: input.newName,
          description: originalCase.description,
          projectId: originalCase.projectId,
          protoFileId: originalCase.protoFileId,
          serviceName: originalCase.serviceName,
          methodName: originalCase.methodName,
          environmentId: originalCase.environmentId,
          requestParams: originalCase.requestParams,
          expectedResult: originalCase.expectedResult,
          createdBy: ctx.user.username,
        },
        include: {
          environment: {
            select: { id: true, name: true },
          },
          createdByUser: {
            select: { username: true, chineseName: true },
          },
        },
      })
    }),
})
