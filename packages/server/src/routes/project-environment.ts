import { z } from 'zod'
import {
  projectProcedure,
  projectWriteProcedure,
  router,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'

export const projectEnvironmentRouter = router({
  // 获取项目绑定的环境列表
  list: projectProcedure
    .meta({ description: '获取项目绑定的环境列表' })
    .query(async ({ ctx }) => {
      return await prisma.projectEnvironment.findMany({
        where: { projectId: ctx.project.id },
        include: {
          environment: {
            include: {
              createdByUser: {
                select: { username: true, chineseName: true },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      })
    }),

  // 绑定环境到项目
  bind: projectWriteProcedure
    .meta({ description: '绑定环境到项目' })
    .input(
      z.object({
        environmentId: z.number().describe('环境ID'),
        customHeaders: z.record(z.string()).optional().describe('项目特定的请求头覆盖'),
        customCookies: z.record(z.string()).optional().describe('项目特定的Cookie覆盖'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      // 检查环境是否存在且用户有权限访问
      const environment = await prisma.environment.findUnique({
        where: { id: input.environmentId },
      })

      if (!environment) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '环境不存在' })
      }

      // 检查用户是否有访问环境的权限
      const admins = (environment.admins as string[]) || []
      const users = (environment.users as string[]) || null
      const hasPermission = 
        environment.createdBy === ctx.user.username ||
        admins.includes(ctx.user.username) ||
        users === null ||
        users.includes(ctx.user.username)

      if (!hasPermission) {
        throw new TRPCError({ code: 'FORBIDDEN', message: '您没有访问此环境的权限' })
      }

      // 检查是否已经绑定
      const existing = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
      })

      if (existing) {
        throw new TRPCError({ code: 'CONFLICT', message: '项目已绑定此环境' })
      }

      return await prisma.projectEnvironment.create({
        data: {
          projectId: ctx.project.id,
          environmentId: input.environmentId,
          customHeaders: input.customHeaders || {},
          customCookies: input.customCookies || {},
        },
        include: {
          environment: {
            include: {
              createdByUser: {
                select: { username: true, chineseName: true },
              },
            },
          },
        },
      })
    }),

  // 更新项目环境绑定配置
  update: projectWriteProcedure
    .meta({ description: '更新项目环境绑定配置' })
    .input(
      z.object({
        environmentId: z.number().describe('环境ID'),
        customHeaders: z.record(z.string()).optional().describe('项目特定的请求头覆盖'),
        customCookies: z.record(z.string()).optional().describe('项目特定的Cookie覆盖'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const binding = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
      })

      if (!binding) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '项目未绑定此环境' })
      }

      return await prisma.projectEnvironment.update({
        where: { id: binding.id },
        data: {
          customHeaders: input.customHeaders,
          customCookies: input.customCookies,
        },
        include: {
          environment: {
            include: {
              createdByUser: {
                select: { username: true, chineseName: true },
              },
            },
          },
        },
      })
    }),

  // 解绑环境
  unbind: projectWriteProcedure
    .meta({ description: '解绑项目环境' })
    .input(z.object({ environmentId: z.number().describe('环境ID') }))
    .mutation(async ({ input, ctx }) => {
      const binding = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
      })

      if (!binding) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '项目未绑定此环境' })
      }

      await prisma.projectEnvironment.delete({
        where: { id: binding.id },
      })

      return { success: true }
    }),

  // 获取项目环境的完整配置（合并环境默认配置和项目覆盖配置）
  getConfig: projectProcedure
    .meta({ description: '获取项目环境的完整配置' })
    .input(z.object({ environmentId: z.number().describe('环境ID') }))
    .query(async ({ input, ctx }) => {
      const binding = await prisma.projectEnvironment.findUnique({
        where: {
          projectId_environmentId: {
            projectId: ctx.project.id,
            environmentId: input.environmentId,
          },
        },
        include: {
          environment: true,
        },
      })

      if (!binding) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '项目未绑定此环境' })
      }

      // 合并配置：项目特定配置覆盖环境默认配置
      const mergedHeaders = {
        ...(binding.environment.headers as Record<string, string> || {}),
        ...(binding.customHeaders as Record<string, string> || {}),
      }

      const mergedCookies = {
        ...(binding.environment.cookies as Record<string, string> || {}),
        ...(binding.customCookies as Record<string, string> || {}),
      }

      return {
        environment: binding.environment,
        binding,
        config: {
          baseUrl: binding.environment.baseUrl,
          headers: mergedHeaders,
          cookies: mergedCookies,
        },
      }
    }),
})
