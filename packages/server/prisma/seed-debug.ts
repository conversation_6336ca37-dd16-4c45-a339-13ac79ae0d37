import { PrismaClient } from './prisma'

const prisma = new PrismaClient()

async function main() {
  console.log('开始创建调试功能的种子数据...')

  // 创建测试用户
  const testUser = await prisma.user.upsert({
    where: { username: 'testuser' },
    update: {},
    create: {
      username: 'testuser',
      chineseName: '测试用户',
    },
  })

  console.log('创建测试用户:', testUser.username)

  // 创建测试仓库
  const testRepo = await prisma.repository.upsert({
    where: { name: 'test-repo' },
    update: {},
    create: {
      name: 'test-repo',
      desc: '测试仓库',
    },
  })

  console.log('创建测试仓库:', testRepo.name)

  // 创建测试项目
  const testProject = await prisma.project.upsert({
    where: {
      repository_name: {
        repository: testRepo.name,
        name: 'test-project',
      },
    },
    update: {},
    create: {
      name: 'test-project',
      desc: '测试项目',
      repository: testRepo.name,
      createdBy: testUser.username,
    },
  })

  console.log('创建测试项目:', testProject.name)

  // 创建测试Proto文件
  const testProtoFile = await prisma.protoFile.upsert({
    where: {
      projectId_name: {
        projectId: testProject.id,
        name: 'test.proto',
      },
    },
    update: {},
    create: {
      name: 'test.proto',
      projectId: testProject.id,
      repository: testRepo.name,
      refreshAt: new Date(),
    },
  })

  console.log('创建测试Proto文件:', testProtoFile.name)

  // 创建测试环境
  const testEnvironment = await prisma.environment.upsert({
    where: { name: '测试环境' },
    update: {},
    create: {
      name: '测试环境',
      description: '用于测试的环境',
      baseUrl: 'https://api.test.com',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-token',
      },
      cookies: {
        session: 'test-session-id',
      },
      createdBy: testUser.username,
      admins: [testUser.username],
      users: undefined, // 公开环境
    },
  })

  console.log('创建测试环境:', testEnvironment.name)

  // 创建开发环境
  const devEnvironment = await prisma.environment.upsert({
    where: { name: '开发环境' },
    update: {},
    create: {
      name: '开发环境',
      description: '开发环境配置',
      baseUrl: 'https://api.dev.com',
      headers: {
        'Content-Type': 'application/json',
      },
      cookies: {},
      createdBy: testUser.username,
      admins: [],
      users: [testUser.username], // 仅限指定用户
    },
  })

  console.log('创建开发环境:', devEnvironment.name)

  // 绑定环境到项目
  const projectEnvBinding1 = await prisma.projectEnvironment.upsert({
    where: {
      projectId_environmentId: {
        projectId: testProject.id,
        environmentId: testEnvironment.id,
      },
    },
    update: {},
    create: {
      projectId: testProject.id,
      environmentId: testEnvironment.id,
      customHeaders: {
        'X-Project-ID': testProject.id.toString(),
      },
      customCookies: {},
    },
  })

  const projectEnvBinding2 = await prisma.projectEnvironment.upsert({
    where: {
      projectId_environmentId: {
        projectId: testProject.id,
        environmentId: devEnvironment.id,
      },
    },
    update: {},
    create: {
      projectId: testProject.id,
      environmentId: devEnvironment.id,
      customHeaders: {},
      customCookies: {
        'dev-session': 'dev-session-id',
      },
    },
  })

  console.log('绑定环境到项目完成')

  // 创建测试用例
  const testCase1 = await prisma.debugCase.create({
    data: {
      name: '获取用户信息测试',
      description: '测试获取用户信息接口',
      projectId: testProject.id,
      protoFileId: testProtoFile.id,
      serviceName: 'TestService',
      methodName: 'GetUser',
      environmentId: testEnvironment.id,
      requestParams: {
        user_id: 123,
      },
      expectedResult: {
        user_id: 123,
        username: 'testuser',
      },
      createdBy: testUser.username,
    },
  })

  const testCase2 = await prisma.debugCase.create({
    data: {
      name: '创建用户测试',
      description: '测试创建用户接口',
      projectId: testProject.id,
      protoFileId: testProtoFile.id,
      serviceName: 'TestService',
      methodName: 'CreateUser',
      environmentId: testEnvironment.id,
      requestParams: {
        username: 'newuser',
      },
      expectedResult: {
        user_id: 456,
        username: 'newuser',
      },
      createdBy: testUser.username,
    },
  })

  console.log('创建测试用例:', testCase1.name, testCase2.name)

  // 创建调试历史记录
  const debugHistory = await prisma.debugHistory.create({
    data: {
      projectId: testProject.id,
      protoFileId: testProtoFile.id,
      serviceName: 'TestService',
      methodName: 'GetUser',
      environmentId: testEnvironment.id,
      debugCaseId: testCase1.id,
      requestUrl: 'https://api.test.com/TestService/GetUser',
      requestMethod: 'POST',
      requestHeaders: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-token',
        'X-Project-ID': testProject.id.toString(),
      },
      requestParams: {
        user_id: 123,
      },
      responseStatus: 200,
      responseHeaders: {
        'Content-Type': 'application/json',
      },
      responseBody: {
        user_id: 123,
        username: 'testuser',
      },
      responseTime: 150,
      executedBy: testUser.username,
    },
  })

  console.log('创建调试历史记录:', debugHistory.id)

  console.log('种子数据创建完成！')
}

main()
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
