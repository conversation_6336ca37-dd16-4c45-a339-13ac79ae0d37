// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  username    String   @id
  chineseName String?
  deptId      Int?
  deptName    String?
  staffId     Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  is_admin    Boolean  @default(false)

  gitAccessToken          String?
  gitAccessTokenExpiresAt DateTime?
  gitRefreshToken         String?

  // API Token for direct API access
  apiToken          String?   @unique
  apiTokenCreatedAt DateTime?

  scriptTemplates ScriptTemplate[]
  aiTemplates     AiTemplate[]
  createdProjects Project[]
  projectMembers  ProjectMember[]

  // 在线调试功能关联
  environments   Environment[]
  debugCases     DebugCase[]
  debugHistories DebugHistory[]
}

model ProjectMember {
  projectId Int
  username  String
  role      ProjectMemberRole
  project   Project           @relation(fields: [projectId], references: [id])
  user      User              @relation(fields: [username], references: [username])

  @@id([projectId, username])
}

enum ProjectMemberRole {
  OWNER
  READ
  WRITE
}

model ScriptTemplate {
  id             Int          @id @default(autoincrement())
  lang           ScriptLang
  script         String       @db.Text
  scriptEs5      String?      @db.Text // 脚本内容转成 ES5 代码
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  createdBy      String
  createdByUser  User         @relation(fields: [createdBy], references: [username])
  auth           TemplateAuth
  title          String
  desc           String       @db.Text
  optimizePrompt String?      @db.Text
  projects       Project[]
}

enum TemplateAuth {
  PUBLIC
  PRIVATE
  OPEN
}

enum ScriptLang {
  JS
  TS
}

model AiTemplate {
  id            Int          @id @default(autoincrement())
  createdAt     DateTime     @default(now())
  createdBy     String
  createdByUser User         @relation(fields: [createdBy], references: [username])
  updatedAt     DateTime     @updatedAt
  title         String
  desc          String       @db.Text
  prompt        String       @db.Text
  projects      Project[]
  auth          TemplateAuth
}

model Repository {
  name String  @id
  desc String?

  projects   Project[]
  protoFiles ProtoFile[]
}

model Project {
  id             Int        @id @default(autoincrement())
  name           String
  repository     String
  repositoryInfo Repository @relation(fields: [repository], references: [name])

  title String?
  desc  String? @db.Text

  createdBy     String
  createdByUser User   @relation(fields: [createdBy], references: [username])

  members         ProjectMember[]
  files           ProtoFile[]
  scriptTemplates ScriptTemplate[]
  aiTemplates     AiTemplate[]

  // 新增字段，用于关联项目下的标签和路径规则
  tags      Tag[] // 项目拥有的所有标签
  pathRules PathRule[] // 项目拥有的所有路径规则

  interfaceMetadata InterfaceMetadata[] // 项目下所有接口的元数据

  // 在线调试功能关联
  projectEnvironments ProjectEnvironment[]
  debugCases          DebugCase[]
  debugHistories      DebugHistory[]

  @@unique([repository, name])
}

model ProtoFile {
  id             Int        @id @default(autoincrement())
  name           String
  repository     String
  repositoryInfo Repository @relation(fields: [repository], references: [name], onDelete: Cascade)
  projectId      Int
  project        Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  // rickId         Int?

  refreshAt DateTime

  histories ProtoHistory[]

  // 新增字段，关联到该文件下接口的元数据
  interfaceMetadata InterfaceMetadata[]

  // 在线调试功能关联
  debugCases     DebugCase[]
  debugHistories DebugHistory[]

  @@unique([projectId, name])
}

model ProtoHistory {
  id       Int       @id @default(autoincrement())
  commitId String
  fileId   Int
  file     ProtoFile @relation(fields: [fileId], references: [id], onDelete: Cascade)

  raw  String  @db.MediumText
  json String  @db.MediumText
  meta String? @db.Text

  updatedAt DateTime
  updatedBy String

  @@unique([commitId, fileId])
}

// --- 新增模型 ---

// 标签模型
model Tag {
  id        Int      @id @default(autoincrement())
  name      String // 标签名称, e.g., "CMS", "Web", "Internal"
  color     String? // 颜色代码, e.g., "#FF0000"
  icon      String? // 图标标识或 URL
  projectId Int // 关联到项目
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  interfaces InterfaceMetadata[]

  @@unique([projectId, name]) // 同一项目下标签名唯一
}

// 路径规则模型
model PathRule {
  id          Int      @id @default(autoincrement())
  name        String // 规则名称, e.g., "Default Nginx Path"
  pattern     String // 路径模板, e.g., "/api/v1/${SERVICE_NAME}/${METHOD_NAME}"
  description String?
  projectId   Int // 关联到项目
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  default     Boolean  @default(false)

  // 被接口元数据引用的关系 (反向)
  interfaceMetadatas InterfaceMetadata[]

  @@unique([projectId, name]) // 同一项目下规则名唯一
}

// 接口元数据模型 (用于存储接口的标签、特定路径规则等)
model InterfaceMetadata {
  id          Int       @id @default(autoincrement())
  projectId   Int
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade) // 冗余 projectId 方便查询
  protoFileId Int
  protoFile   ProtoFile @relation(fields: [protoFileId], references: [id], onDelete: Cascade) // Proto文件删除时，此元数据也删除
  serviceName String // e.g., "UserService"
  methodName  String // e.g., "GetUser"

  pathRuleId Int? // 此接口特定的路径规则 ID (可选)
  pathRule   PathRule? @relation(fields: [pathRuleId], references: [id], onDelete: SetNull) // PathRule 删除时，这里设为 null

  tags Tag[] // 分配给此接口的标签

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([protoFileId, serviceName, methodName]) // 同一 Proto 文件内，服务名+方法名唯一确定一条元数据
}

// --- 在线调试功能相关模型 ---

// 环境配置模型
model Environment {
  id          Int     @id @default(autoincrement())
  name        String // 环境名称, e.g., "测试环境", "预发布环境"
  description String? // 环境描述
  baseUrl     String // 基础URL, e.g., "https://api.test.com"

  // 请求头配置 (JSON格式存储)
  headers Json? // 默认请求头, e.g., {"Content-Type": "application/json"}

  // Cookie配置 (JSON格式存储，支持登录后自动保存)
  cookies Json? // Cookie信息, e.g., {"session": "xxx", "token": "yyy"}

  // 权限控制
  createdBy     String // 创建者用户名
  createdByUser User   @relation(fields: [createdBy], references: [username])

  // 环境管理员列表 (JSON数组格式)
  admins Json? // 管理员用户名列表, e.g., ["admin1", "admin2"]

  // 环境使用者列表 (JSON数组格式，空表示所有人可用)
  users Json? // 使用者用户名列表, e.g., ["user1", "user2"], null表示公开

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  projectEnvironments ProjectEnvironment[]
  debugCases          DebugCase[]
  debugHistories      DebugHistory[]

  @@unique([name]) // 环境名称全局唯一
}

// 项目环境关联模型
model ProjectEnvironment {
  id            Int         @id @default(autoincrement())
  projectId     Int
  project       Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  environmentId Int
  environment   Environment @relation(fields: [environmentId], references: [id], onDelete: Cascade)

  // 项目特定的配置覆盖
  customHeaders Json? // 项目特定的请求头覆盖
  customCookies Json? // 项目特定的Cookie覆盖

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([projectId, environmentId]) // 同一项目不能重复绑定同一环境
}

// 调试用例模型
model DebugCase {
  id          Int     @id @default(autoincrement())
  name        String // 用例名称
  description String? // 用例描述

  // 接口信息
  projectId   Int
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  protoFileId Int
  protoFile   ProtoFile @relation(fields: [protoFileId], references: [id], onDelete: Cascade)
  serviceName String // 服务名
  methodName  String // 方法名

  // 环境信息
  environmentId Int
  environment   Environment @relation(fields: [environmentId], references: [id], onDelete: Cascade)

  // 请求参数 (JSON格式)
  requestParams Json // 请求参数, e.g., {"userId": 123, "name": "test"}

  // 期望结果 (可选)
  expectedResult Json? // 期望的返回结果

  // 创建者
  createdBy     String
  createdByUser User   @relation(fields: [createdBy], references: [username])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联的调试历史
  debugHistories DebugHistory[]
}

// 调试历史记录模型
model DebugHistory {
  id Int @id @default(autoincrement())

  // 关联信息
  projectId   Int
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  protoFileId Int
  protoFile   ProtoFile @relation(fields: [protoFileId], references: [id], onDelete: Cascade)
  serviceName String // 服务名
  methodName  String // 方法名

  // 环境信息
  environmentId Int
  environment   Environment @relation(fields: [environmentId], references: [id], onDelete: Cascade)

  // 用例信息 (可选，如果是从用例执行的)
  debugCaseId Int?
  debugCase   DebugCase? @relation(fields: [debugCaseId], references: [id], onDelete: SetNull)

  // 请求信息
  requestUrl     String // 完整的请求URL
  requestMethod  String // 请求方法, e.g., "POST", "GET"
  requestHeaders Json // 实际发送的请求头
  requestParams  Json // 请求参数

  // 响应信息
  responseStatus  Int? // HTTP状态码
  responseHeaders Json? // 响应头
  responseBody    Json? // 响应体
  responseTime    Int? // 响应时间(毫秒)

  // 错误信息
  errorMessage String? // 错误信息

  // 执行者
  executedBy     String
  executedByUser User   @relation(fields: [executedBy], references: [username])

  createdAt DateTime @default(now())
}
