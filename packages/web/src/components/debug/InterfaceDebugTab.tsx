import { useState } from 'react'
import {
  Stack,
  Group,
  Button,
  Select,
  JsonInput,
  Card,
  Text,
  Badge,
  Alert,
  Loader,
  Tabs,
  TextInput,
  ActionIcon,
  Menu,
  Modal,
  Textarea,
} from '@mantine/core'
import {
  IconPlayCard,
  IconHistory,
  IconBookmark,
  IconDots,
  IconEdit,
  IconTrash,
  IconCopy,
  IconAlertCircle,
  IconCheck,
  IconX,
} from '@tabler/icons-react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { trpc, type ApiData } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { useForm } from '@mantine/form'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface InterfaceDebugTabProps {
  projectId: number
  protoFileId: number
  serviceName: string
  methodName: string
}

export function InterfaceDebugTab({
  projectId,
  protoFileId,
  serviceName,
  methodName,
}: InterfaceDebugTabProps) {
  const [activeTab, setActiveTab] = useState<string | null>('debug')
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>('')
  const [saveCaseModalOpened, setSaveCaseModalOpened] = useState(false)
  const [debugResult, setDebugResult] = useState<any>(null)

  // 获取项目绑定的环境列表
  const { data: environments = [] } = useQuery(
    trpc.projectEnvironment.list.queryOptions({ projectId }),
  )

  // 获取调试历史
  const { data: historyData, refetch: refetchHistory } = useQuery(
    trpc.debug.getHistory.queryOptions(
      {
        projectId,
        protoFileId,
        serviceName,
        methodName,
        environmentId: selectedEnvironmentId ? parseInt(selectedEnvironmentId) : undefined,
        limit: 10,
      },

      { enabled: activeTab === 'history' },
    ),
  )

  // 获取调试用例
  const { data: cases = [], refetch: refetchCases } = useQuery(
    trpc.debugCase.list.queryOptions(
      {
        projectId,
        protoFileId,
        serviceName,
        methodName,
        environmentId: selectedEnvironmentId ? parseInt(selectedEnvironmentId) : undefined,
      },
      { enabled: activeTab === 'cases' },
    ),
  )

  // 执行调试
  const debugMutation = useMutation(
    trpc.debug.execute.mutationOptions({
      onSuccess: data => {
        setDebugResult(data.result)
        refetchHistory()
        notifications.show({
          title: '调试完成',
          message: data.result.success ? '请求成功' : '请求失败',
          color: data.result.success ? 'green' : 'red',
        })
      },
      onError: error => {
        notifications.show({
          title: '调试失败',
          message: error.message,
          color: 'red',
        })
      },
    }),
  )

  // 执行用例
  const executeCaseMutation = useMutation(
    trpc.debugCase.execute.mutationOptions({
      onSuccess: data => {
        setDebugResult(data.result)
        refetchHistory()
        notifications.show({
          title: '用例执行完成',
          message: data.result.success ? '请求成功' : '请求失败',
          color: data.result.success ? 'green' : 'red',
        })
      },
      onError: error => {
        notifications.show({
          title: '用例执行失败',
          message: error.message,
          color: 'red',
        })
      },
    }),
  )

  const form = useForm({
    initialValues: {
      requestParams: '{}',
    },
    validate: {
      requestParams: value => {
        try {
          JSON.parse(value)
          return null
        } catch {
          return '请输入有效的JSON格式'
        }
      },
    },
  })

  const handleDebug = form.onSubmit(values => {
    if (!selectedEnvironmentId) {
      notifications.show({
        title: '请选择环境',
        message: '请先选择一个调试环境',
        color: 'orange',
      })
      return
    }

    try {
      const requestParams = JSON.parse(values.requestParams)
      debugMutation.mutate({
        projectId,
        protoFileId,
        serviceName,
        methodName,
        environmentId: parseInt(selectedEnvironmentId),
        requestParams,
      })
    } catch (error) {
      notifications.show({
        title: '参数格式错误',
        message: '请检查JSON格式是否正确',
        color: 'red',
      })
    }
  })

  const handleExecuteCase = (caseItem: any) => {
    executeCaseMutation.mutate({ id: caseItem.id })
  }

  const handleSaveAsCase = () => {
    if (!debugResult) {
      notifications.show({
        title: '没有可保存的结果',
        message: '请先执行调试',
        color: 'orange',
      })
      return
    }
    setSaveCaseModalOpened(true)
  }

  if (environments.length === 0) {
    return (
      <Alert icon={<IconAlertCircle size="1rem" />} color="blue">
        <Text size="sm">
          项目还没有绑定任何调试环境。请先在"环境管理"页面绑定环境后再进行调试。
        </Text>
      </Alert>
    )
  }

  return (
    <Stack gap="md">
      {/* 环境选择 */}
      <Select
        label="选择调试环境"
        placeholder="请选择环境"
        value={selectedEnvironmentId}
        onChange={value => setSelectedEnvironmentId(value || '')}
        data={environments.map(env => ({
          value: env.environment.id.toString(),
          label: `${env.environment.name} (${env.environment.baseUrl})`,
        }))}
        required
      />

      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="debug" leftSection={<IconPlayCard size={16} />}>
            在线调试
          </Tabs.Tab>
          <Tabs.Tab value="cases" leftSection={<IconBookmark size={16} />}>
            测试用例
          </Tabs.Tab>
          <Tabs.Tab value="history" leftSection={<IconHistory size={16} />}>
            调试历史
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="debug" pt="md">
          <form onSubmit={handleDebug}>
            <Stack gap="md">
              <JsonInput
                label="请求参数"
                placeholder='{"key": "value"}'
                validationError="请输入有效的JSON格式"
                formatOnBlur
                autosize
                minRows={6}
                maxRows={15}
                {...form.getInputProps('requestParams')}
              />

              <Group>
                <Button
                  type="submit"
                  leftSection={<IconPlayCard size={16} />}
                  loading={debugMutation.isPending}
                  disabled={!selectedEnvironmentId}
                >
                  执行调试
                </Button>
                {debugResult && (
                  <Button
                    variant="light"
                    leftSection={<IconBookmark size={16} />}
                    onClick={handleSaveAsCase}
                  >
                    保存为用例
                  </Button>
                )}
              </Group>

              {/* 调试结果 */}
              {debugResult && <DebugResult result={debugResult} />}
            </Stack>
          </form>
        </Tabs.Panel>

        <Tabs.Panel value="cases" pt="md">
          <Stack gap="md">
            {cases.length > 0 ? (
              cases.map(caseItem => (
                <CaseCard
                  key={caseItem.id}
                  case={caseItem}
                  onExecute={handleExecuteCase}
                  onRefresh={refetchCases}
                  isExecuting={executeCaseMutation.isPending}
                />
              ))
            ) : (
              <Alert icon={<IconAlertCircle size="1rem" />} color="blue">
                <Text size="sm">还没有保存的测试用例。执行调试后可以保存为用例。</Text>
              </Alert>
            )}
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="history" pt="md">
          <Stack gap="md">
            {historyData?.histories && historyData.histories.length > 0 ? (
              historyData.histories.map(history => (
                <HistoryCard key={history.id} history={history} />
              ))
            ) : (
              <Alert icon={<IconAlertCircle size="1rem" />} color="blue">
                <Text size="sm">还没有调试历史记录。</Text>
              </Alert>
            )}
          </Stack>
        </Tabs.Panel>
      </Tabs>

      {/* 保存用例弹窗 */}
      <SaveCaseModal
        opened={saveCaseModalOpened}
        onClose={() => setSaveCaseModalOpened(false)}
        debugResult={debugResult}
        projectId={projectId}
        protoFileId={protoFileId}
        serviceName={serviceName}
        methodName={methodName}
        environmentId={selectedEnvironmentId ? parseInt(selectedEnvironmentId) : 0}
        onSuccess={() => {
          refetchCases()
          setSaveCaseModalOpened(false)
        }}
      />
    </Stack>
  )
}

// 调试结果组件
function DebugResult({ result }: { result: any }) {
  const getStatusColor = (status: number | null) => {
    if (!status) return 'gray'
    if (status >= 200 && status < 300) return 'green'
    if (status >= 400) return 'red'
    return 'orange'
  }

  return (
    <Card shadow="sm" padding="md" radius="md" withBorder>
      <Stack gap="md">
        <Group justify="space-between">
          <Text fw={500}>调试结果</Text>
          <Group>
            {result.responseStatus && (
              <Badge color={getStatusColor(result.responseStatus)}>{result.responseStatus}</Badge>
            )}
            <Badge variant="light">{result.responseTime}ms</Badge>
            {result.success ? (
              <IconCheck size={16} color="green" />
            ) : (
              <IconX size={16} color="red" />
            )}
          </Group>
        </Group>

        {result.errorMessage && (
          <Alert icon={<IconAlertCircle size="1rem" />} color="red">
            {result.errorMessage}
          </Alert>
        )}

        {result.responseBody && (
          <JsonInput
            label="响应内容"
            value={JSON.stringify(result.responseBody, null, 2)}
            readOnly
            autosize
            minRows={4}
            maxRows={20}
          />
        )}
      </Stack>
    </Card>
  )
}

// 用例卡片组件
function CaseCard({ case: caseItem, onExecute, onRefresh, isExecuting }: any) {
  return (
    <Card shadow="sm" padding="md" radius="md" withBorder>
      <Group justify="space-between" mb="xs">
        <div>
          <Text fw={500}>{caseItem.name}</Text>
          {caseItem.description && (
            <Text size="sm" c="dimmed">
              {caseItem.description}
            </Text>
          )}
        </div>
        <Group>
          <Badge variant="light" size="sm">
            {caseItem.environment.name}
          </Badge>
          <Button
            size="xs"
            leftSection={<IconPlayCard size={14} />}
            onClick={() => onExecute(caseItem)}
            loading={isExecuting}
          >
            执行
          </Button>
        </Group>
      </Group>

      <Text size="xs" c="dimmed">
        创建于{' '}
        {formatDistanceToNow(new Date(caseItem.createdAt), {
          addSuffix: true,
          locale: zhCN,
        })}{' '}
        by {caseItem.createdByUser.username}
      </Text>
    </Card>
  )
}

// 历史记录卡片组件
function HistoryCard({ history }: any) {
  const getStatusColor = (status: number | null) => {
    if (!status) return 'gray'
    if (status >= 200 && status < 300) return 'green'
    if (status >= 400) return 'red'
    return 'orange'
  }

  return (
    <Card shadow="sm" padding="md" radius="md" withBorder>
      <Group justify="space-between" mb="xs">
        <div>
          <Group>
            <Text fw={500}>{history.requestMethod}</Text>
            <Text size="sm" ff="monospace">
              {history.requestUrl}
            </Text>
          </Group>
          {history.debugCase && (
            <Badge variant="light" size="sm">
              用例: {history.debugCase.name}
            </Badge>
          )}
        </div>
        <Group>
          {history.responseStatus && (
            <Badge color={getStatusColor(history.responseStatus)}>{history.responseStatus}</Badge>
          )}
          <Badge variant="light">{history.responseTime}ms</Badge>
        </Group>
      </Group>

      <Text size="xs" c="dimmed">
        执行于{' '}
        {formatDistanceToNow(new Date(history.createdAt), {
          addSuffix: true,
          locale: zhCN,
        })}{' '}
        by {history.executedByUser.username} 在 {history.environment.name}
      </Text>
    </Card>
  )
}

// 保存用例弹窗组件
function SaveCaseModal({
  opened,
  onClose,
  debugResult,
  projectId,
  protoFileId,
  serviceName,
  methodName,
  environmentId,
  onSuccess,
}: any) {
  const form = useForm({
    initialValues: {
      name: '',
      description: '',
    },
    validate: {
      name: value => (!value ? '用例名称不能为空' : null),
    },
  })

  const createCaseMutation = useMutation(
    trpc.debugCase.create.mutationOptions({
      onSuccess: () => {
        notifications.show({
          title: '保存成功',
          message: '测试用例已保存',
          color: 'green',
        })
        onSuccess()
        form.reset()
      },
      onError: error => {
        notifications.show({
          title: '保存失败',
          message: error.message,
          color: 'red',
        })
      },
    }),
  )

  const handleSubmit = form.onSubmit(values => {
    createCaseMutation.mutate({
      name: values.name,
      description: values.description || undefined,
      projectId,
      protoFileId,
      serviceName,
      methodName,
      environmentId,
      requestParams: debugResult.requestParams,
      expectedResult: debugResult.responseBody,
    })
  })

  return (
    <Modal opened={opened} onClose={onClose} title="保存为测试用例" centered>
      <form onSubmit={handleSubmit}>
        <Stack gap="md">
          <TextInput
            label="用例名称"
            placeholder="输入用例名称"
            required
            {...form.getInputProps('name')}
          />

          <Textarea
            label="用例描述"
            placeholder="输入用例描述（可选）"
            rows={3}
            {...form.getInputProps('description')}
          />

          <Group justify="flex-end">
            <Button variant="subtle" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" loading={createCaseMutation.isPending}>
              保存
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}
