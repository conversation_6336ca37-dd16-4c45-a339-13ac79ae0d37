import {
  Box,
  Text,
  Group,
  Button,
  <PERSON>ack,
  ActionIcon,
  TextInput,
  NumberInput,
  Switch,
  JsonInput,
} from '@mantine/core'
import { IconPlus, IconTrash } from '@tabler/icons-react'

// 参数表单组件
interface ParameterFormProps {
  schema: any
  value: any
  onChange: (value: any) => void
  name: string
  level?: number
}

export function ParameterForm({ schema, value, onChange, name, level = 0 }: ParameterFormProps) {
  const handleArrayChange = (index: number, newValue: any) => {
    const newArray = [...(value || [])]
    newArray[index] = newValue
    onChange(newArray)
  }

  const addArrayItem = () => {
    const newArray = [...(value || [])]
    newArray.push(getDefaultValueForSchema(schema.items))
    onChange(newArray)
  }

  const removeArrayItem = (index: number) => {
    const newArray = [...(value || [])]
    newArray.splice(index, 1)
    onChange(newArray)
  }

  const handleObjectChange = (key: string, newValue: any) => {
    onChange({
      ...(value || {}),
      [key]: newValue,
    })
  }

  const getDefaultValueForSchema = (schema: any): any => {
    switch (schema.type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'array':
        return []
      case 'object':
        const obj: Record<string, any> = {}
        if (schema.properties) {
          Object.entries(schema.properties).forEach(([key, childSchema]: [string, any]) => {
            obj[key] = getDefaultValueForSchema(childSchema)
          })
        }
        return obj
      default:
        return null
    }
  }

  if (schema.type === 'string') {
    return (
      <TextInput
        label={name}
        value={value || ''}
        onChange={e => onChange(e.target.value)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'number') {
    return (
      <NumberInput
        label={name}
        value={value || 0}
        onChange={val => onChange(val)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'boolean') {
    return (
      <Switch
        label={name}
        checked={value || false}
        onChange={e => onChange(e.target.checked)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'array') {
    return (
      <Box style={{ marginLeft: level * 20 }}>
        <Group justify="space-between" mb="xs">
          <Text size="sm" fw={500}>
            {name} (数组)
          </Text>
          <Button
            size="xs"
            variant="light"
            leftSection={<IconPlus size={14} />}
            onClick={addArrayItem}
          >
            添加项
          </Button>
        </Group>
        {schema.description && (
          <Text size="xs" c="dimmed" mb="sm">
            {schema.description}
          </Text>
        )}
        <Stack gap="sm">
          {(value || []).map((item: any, index: number) => (
            <Group key={index} align="flex-start" wrap="nowrap">
              <Box style={{ flex: 1 }}>
                <ParameterForm
                  schema={schema.items}
                  value={item}
                  onChange={newValue => handleArrayChange(index, newValue)}
                  name={`[${index}]`}
                  level={0}
                />
              </Box>
              <ActionIcon
                color="red"
                variant="light"
                onClick={() => removeArrayItem(index)}
                mt="xl"
              >
                <IconTrash size={14} />
              </ActionIcon>
            </Group>
          ))}
        </Stack>
      </Box>
    )
  }

  if (schema.type === 'object') {
    return (
      <Box style={{ marginLeft: level * 20 }}>
        <Text size="sm" fw={500} mb="xs">
          {name} (对象)
        </Text>
        {schema.description && (
          <Text size="xs" c="dimmed" mb="sm">
            {schema.description}
          </Text>
        )}
        <Stack gap="sm">
          {schema.properties &&
            Object.entries(schema.properties).map(([key, childSchema]: [string, any]) => (
              <ParameterForm
                key={key}
                schema={childSchema}
                value={value?.[key]}
                onChange={newValue => handleObjectChange(key, newValue)}
                name={key}
                level={0}
              />
            ))}
        </Stack>
      </Box>
    )
  }

  // 其他类型使用JSON输入
  return (
    <JsonInput
      label={name}
      value={JSON.stringify(value, null, 2)}
      onChange={val => {
        try {
          onChange(JSON.parse(val))
        } catch {
          // 忽略解析错误
        }
      }}
      description={schema.description}
      style={{ marginLeft: level * 20 }}
      autosize
      minRows={2}
    />
  )
}
