import {
  Paper,
  Text,
  Badge,
  Group,
  Box,
  Code,
  CopyButton,
  ActionIcon,
  Tooltip,
  Stack,
  Button,
} from '@mantine/core'
import { IconCopy, IconCheck, IconPlayerPlay } from '@tabler/icons-react'

import { useModal } from '@/utils/modal'
import { ParametersTable } from './ParametersTable'
import { ApiCallModal } from './ApiCallModal'

interface ApiProcedure {
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
    additionalProperties: boolean
    $schema: string
  }
  nodeType: 'procedure'
  procedureType: 'query' | 'mutation'
  pathFromRootRouter: string[]
  extraData: {
    description?: string
    parameterDescriptions: Record<string, string>
  }
}

interface ApiProcedureCardProps {
  name: string
  procedure: ApiProcedure
  userToken?: string
}

export function ApiProcedureCard({ name, procedure, userToken }: ApiProcedureCardProps) {
  const path = procedure.pathFromRootRouter.join('.')
  const isQuery = procedure.procedureType === 'query'
  const method = isQuery ? 'GET' : 'POST'

  // 使用 useModal 创建API调用弹框
  const apiCallModal = useModal(ApiCallModal)

  const generateCurlCommand = () => {
    const hasParams =
      procedure.inputSchema.properties && Object.keys(procedure.inputSchema.properties).length > 0
    // 如果用户有Token就使用真实Token，否则使用占位符
    const token = userToken || 'YOUR_TOKEN'

    if (isQuery) {
      // GET请求，参数放在URL中
      let queryParams = ''
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        queryParams = `?input=${encodeURIComponent(JSON.stringify(exampleParams))}`
      } else {
        queryParams = `?input=${encodeURIComponent('{}')}`
      }

      return `curl -X GET "${window.location.origin}/api/trpc/${path}${queryParams}" \\
  -H "Authorization: Bearer ${token}"`
    } else {
      // POST请求，参数直接放在body中
      let body = '{}'
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        body = JSON.stringify(exampleParams)
      }

      return `curl -X POST "${window.location.origin}/api/trpc/${path}" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${token}" \\
  -d '${body}'`
    }
  }

  // 打开API调用弹框
  const handleOpenApiCall = () => {
    apiCallModal.openModal({
      procedure,
      name,
      userToken,
    })
  }

  return (
    <Paper p="md" withBorder radius="md" mb="md">
      <Stack gap="md">
        {/* 接口标题和操作 */}
        <Group justify="space-between" align="flex-start">
          <Box>
            <Group gap="xs" mb="xs">
              <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
                {method}
              </Badge>
              <Text fw={500} size="lg">
                {procedure.extraData.description ?? name}
              </Text>
            </Group>
            <Code>
              <Text component="span" c="dimmed" size="xs">
                {location.origin}/api/trpc/
              </Text>
              <Text component="span" fw={700} size="xs">
                {path}
              </Text>
            </Code>
          </Box>
          <Group gap="xs">
            <Button
              variant="filled"
              color="green"
              leftSection={<IconPlayerPlay size={14} />}
              onClick={handleOpenApiCall}
            >
              调用接口
            </Button>
            <CopyButton value={generateCurlCommand()}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? '已复制curl命令' : '复制curl命令'}>
                  <ActionIcon color={copied ? 'teal' : 'blue'} variant="light" onClick={copy}>
                    {copied ? <IconCheck size={14} /> : <IconCopy size={14} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>

        {/* 请求方法说明 */}
        {/* <Group gap="xs">
          <Text size="sm" c="dimmed">
            请求方法：
          </Text>
          <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
            {method}
          </Badge>
          <Text size="xs" c="dimmed">
            {isQuery ? '(Query接口固定使用GET方法)' : '(Mutation接口固定使用POST方法)'}
          </Text>
        </Group> */}

        {/* 参数表格 */}
        <Box>
          <Text size="sm" fw={500} mb="xs">
            请求参数：
          </Text>
          <ParametersTable
            inputSchema={procedure.inputSchema}
            parameterDescriptions={procedure.extraData.parameterDescriptions}
          />
        </Box>
      </Stack>

      {/* 弹框会自动渲染，无需手动渲染 */}
    </Paper>
  )
}
