import { Tabs, Badge, Stack, Text } from '@mantine/core'
import { ApiProcedureCard } from './ApiProcedureCard'

interface ApiProcedure {
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
    additionalProperties: boolean
    $schema: string
  }
  nodeType: 'procedure'
  procedureType: 'query' | 'mutation'
  pathFromRootRouter: string[]
  extraData: {
    parameterDescriptions: Record<string, string>
  }
}

interface ApiRouter {
  children: Record<string, ApiProcedure | ApiRouter>
  nodeType: 'router'
  path: string[]
}

interface ApiTabsProps {
  data: Record<string, ApiRouter>
  userToken?: string
  level?: number
}

export function ApiTabs({ data, userToken, level = 0 }: ApiTabsProps) {
  const renderTabContent = (name: string, router: ApiRouter) => {
    const procedures: Array<[string, ApiProcedure]> = []
    const subRouters: Array<[string, ApiRouter]> = []

    // 分离procedures和subRouters
    Object.entries(router.children).forEach(([key, value]) => {
      if (value.nodeType === 'procedure') {
        procedures.push([key, value as ApiProcedure])
      } else {
        subRouters.push([key, value as ApiRouter])
      }
    })

    return (
      <Stack gap="md">
        {/* 如果有子路由，渲染嵌套的Tabs */}
        {subRouters.length > 0 && (
          <ApiTabs data={Object.fromEntries(subRouters)} userToken={userToken} level={level + 1} />
        )}

        {/* 渲染当前层级的procedures */}
        {procedures.length > 0 && (
          <Stack gap="sm">
            {subRouters.length > 0 && (
              <Text size="sm" fw={500} c="dimmed" mt="md">
                {name} 直接接口：
              </Text>
            )}
            {procedures.map(([procName, procedure]) => (
              <ApiProcedureCard
                key={procName}
                name={procName}
                procedure={procedure}
                userToken={userToken}
              />
            ))}
          </Stack>
        )}

        {/* 如果既没有子路由也没有procedures */}
        {procedures.length === 0 && subRouters.length === 0 && (
          <Text size="sm" c="dimmed" ta="center" py="xl">
            该模块暂无接口
          </Text>
        )}
      </Stack>
    )
  }

  const getTabLabel = (name: string, router: ApiRouter) => {
    const procedures: Array<[string, ApiProcedure]> = []
    const subRouters: Array<[string, ApiRouter]> = []

    Object.entries(router.children).forEach(([key, value]) => {
      if (value.nodeType === 'procedure') {
        procedures.push([key, value as ApiProcedure])
      } else {
        subRouters.push([key, value as ApiRouter])
      }
    })

    // 计算总的接口数量（包括子路由中的）
    const getTotalProcedures = (router: ApiRouter): number => {
      let count = 0
      Object.values(router.children).forEach(child => {
        if (child.nodeType === 'procedure') {
          count++
        } else {
          count += getTotalProcedures(child as ApiRouter)
        }
      })
      return count
    }

    const totalCount = getTotalProcedures(router)

    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <span>{name}</span>
        {totalCount > 0 && (
          <Badge size="xs" variant="light" color="blue">
            {totalCount}
          </Badge>
        )}
      </div>
    )
  }

  const tabEntries = Object.entries(data)

  if (tabEntries.length === 0) {
    return (
      <Text size="sm" c="dimmed" ta="center" py="xl">
        暂无接口数据
      </Text>
    )
  }

  // 如果只有一个tab，直接渲染内容，不显示tab标签
  if (tabEntries.length === 1) {
    const [name, router] = tabEntries[0]
    return renderTabContent(name, router)
  }

  return (
    <Tabs
      defaultValue={tabEntries[0][0]}
      variant={level === 0 ? 'default' : 'pills'}
      orientation={level >= 2 ? 'vertical' : 'horizontal'}
    >
      <Tabs.List>
        {tabEntries.map(([name, router]) => (
          <Tabs.Tab key={name} value={name}>
            {getTabLabel(name, router)}
          </Tabs.Tab>
        ))}
      </Tabs.List>

      {tabEntries.map(([name, router]) => (
        <Tabs.Panel key={name} value={name} pt="md">
          {renderTabContent(name, router)}
        </Tabs.Panel>
      ))}
    </Tabs>
  )
}
