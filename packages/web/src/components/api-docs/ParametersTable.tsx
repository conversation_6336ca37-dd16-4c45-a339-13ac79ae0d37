import { Table, Text } from '@mantine/core'
import { ParameterRow } from './ParameterRow'

interface ParametersTableProps {
  inputSchema: {
    properties?: Record<string, any>
    required?: string[]
  }
  parameterDescriptions?: Record<string, string>
}

export function ParametersTable({ inputSchema, parameterDescriptions }: ParametersTableProps) {
  if (!inputSchema.properties || Object.keys(inputSchema.properties).length === 0) {
    return (
      <Text size="sm" c="dimmed">
        无参数
      </Text>
    )
  }

  const rows = Object.entries(inputSchema.properties).map(([key, value]: [string, any]) => (
    <ParameterRow
      key={key}
      name={key}
      schema={value}
      required={inputSchema.required?.includes(key) || false}
      description={parameterDescriptions?.[key] || value.description}
    />
  ))

  return (
    <Table>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>参数名</Table.Th>
          <Table.Th>类型</Table.Th>
          <Table.Th>必填</Table.Th>
          <Table.Th>描述</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>{rows}</Table.Tbody>
    </Table>
  )
}
