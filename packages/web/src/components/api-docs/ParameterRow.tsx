import { useState } from 'react'
import {
  Table,
  Badge,
  Text,
  Group,
  ActionIcon,
  Code,
} from '@mantine/core'
import { IconChevronDown, IconChevronRight } from '@tabler/icons-react'

// 递归渲染参数的组件
interface ParameterRowProps {
  name: string
  schema: any
  required: boolean
  level?: number
  description?: string
}

export function ParameterRow({ name, schema, required, level = 0, description }: ParameterRowProps) {
  const [expanded, setExpanded] = useState(level < 2)
  const hasChildren = schema.type === 'object' || schema.type === 'array'
  
  const getTypeDisplay = (schema: any): string => {
    if (schema.type === 'array') {
      if (schema.items) {
        return `${getTypeDisplay(schema.items)}[]`
      }
      return 'array'
    }
    return schema.type || 'unknown'
  }

  const renderChildren = () => {
    if (schema.type === 'object' && schema.properties) {
      return Object.entries(schema.properties).map(([childName, childSchema]: [string, any]) => (
        <ParameterRow
          key={childName}
          name={childName}
          schema={childSchema}
          required={schema.required?.includes(childName) || false}
          level={level + 1}
          description={childSchema.description}
        />
      ))
    }
    
    if (schema.type === 'array' && schema.items) {
      return (
        <ParameterRow
          key="[item]"
          name="[item]"
          schema={schema.items}
          required={true}
          level={level + 1}
          description={schema.items.description}
        />
      )
    }
    
    return null
  }

  return (
    <>
      <Table.Tr style={{ backgroundColor: level > 0 ? 'var(--mantine-color-gray-0)' : undefined }}>
        <Table.Td style={{ paddingLeft: `${level * 20 + 12}px` }}>
          <Group gap="xs" wrap="nowrap">
            {hasChildren && (
              <ActionIcon size="xs" variant="subtle" onClick={() => setExpanded(!expanded)}>
                {expanded ? <IconChevronDown size={12} /> : <IconChevronRight size={12} />}
              </ActionIcon>
            )}
            <Code>{name}</Code>
          </Group>
        </Table.Td>
        <Table.Td>
          <Badge
            size="xs"
            variant="light"
            color={
              schema.type === 'string'
                ? 'blue'
                : schema.type === 'number'
                  ? 'green'
                  : schema.type === 'boolean'
                    ? 'orange'
                    : 'gray'
            }
          >
            {getTypeDisplay(schema)}
          </Badge>
        </Table.Td>
        <Table.Td>
          {required ? (
            <Badge size="xs" color="red" variant="light">
              必填
            </Badge>
          ) : (
            <Badge size="xs" color="gray" variant="light">
              可选
            </Badge>
          )}
        </Table.Td>
        <Table.Td>
          <Text size="sm" c="dimmed">
            {description || schema.description || '-'}
          </Text>
        </Table.Td>
      </Table.Tr>
      {hasChildren && expanded && renderChildren()}
    </>
  )
}
