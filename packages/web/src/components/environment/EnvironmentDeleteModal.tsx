import { <PERSON><PERSON>, <PERSON>, Button, Group, Alert, Stack } from '@mantine/core'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertTriangle } from '@tabler/icons-react'

interface EnvironmentDeleteModalProps {
  environment: any
  onSuccess?: () => void
  onClose: () => void
}

export function EnvironmentDeleteModal({
  environment,
  onSuccess,
  onClose,
}: EnvironmentDeleteModalProps) {
  const deleteMutation = useMutation(
    trpc.environment.delete.mutationOptions({
      onSuccess: () => {
        notifications.show({
          title: '删除成功',
          message: '环境已删除',
          color: 'green',
        })
        onSuccess?.()
        onClose()
      },
      onError: error => {
        notifications.show({
          title: '删除失败',
          message: error.message,
          color: 'red',
        })
      },
    }),
  )

  const handleDelete = () => {
    deleteMutation.mutate({ id: environment.id })
  }

  const hasUsage = environment._count.projectEnvironments > 0 || environment._count.debugCases > 0

  return (
    <Modal opened onClose={onClose} title="删除环境" centered>
      <Stack gap="md">
        <Alert icon={<IconAlertTriangle size="1rem" />} color="red">
          <Text size="sm">
            您确定要删除环境 <strong>"{environment.name}"</strong> 吗？
          </Text>
        </Alert>

        {hasUsage && (
          <Alert color="orange">
            <Text size="sm">
              <strong>注意：</strong>此环境正在被使用中：
            </Text>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {environment._count.projectEnvironments > 0 && (
                <li>{environment._count.projectEnvironments} 个项目绑定了此环境</li>
              )}
              {environment._count.debugCases > 0 && (
                <li>{environment._count.debugCases} 个调试用例使用了此环境</li>
              )}
            </ul>
            <Text size="sm">删除后，相关的项目绑定和调试用例也会被清理。</Text>
          </Alert>
        )}

        <Text size="sm" c="dimmed">
          此操作不可撤销，请谨慎操作。
        </Text>

        <Group justify="flex-end">
          <Button variant="subtle" onClick={onClose} disabled={deleteMutation.isPending}>
            取消
          </Button>
          <Button color="red" onClick={handleDelete} loading={deleteMutation.isPending}>
            确认删除
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}
