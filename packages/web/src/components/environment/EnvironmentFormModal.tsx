import { useState } from 'react'
import {
  Modal,
  Stack,
  TextInput,
  Textarea,
  Button,
  Group,
  Alert,
  Tabs,
  JsonInput,
  MultiSelect,
  Switch,
} from '@mantine/core'
import { useForm } from '@mantine/form'
import { useMutation, useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { notifications } from '@mantine/notifications'
import { IconAlertCircle, IconServer, IconSettings, IconUsers } from '@tabler/icons-react'

interface EnvironmentFormModalProps {
  environment?: any
  onSuccess?: () => void
  onClose: () => void
}

export function EnvironmentFormModal({
  environment,
  onSuccess,
  onClose,
}: EnvironmentFormModalProps) {
  const [activeTab, setActiveTab] = useState<string | null>('basic')
  const isEditing = !!environment

  // 获取用户列表（用于权限配置）
  const { data: users = [] } = useQuery(
    trpc.user.list.queryOptions(undefined, { enabled: false }) // 暂时禁用，需要实现用户列表API
  )

  const form = useForm({
    initialValues: {
      name: environment?.name || '',
      description: environment?.description || '',
      baseUrl: environment?.baseUrl || '',
      headers: environment?.headers ? JSON.stringify(environment.headers, null, 2) : '{}',
      cookies: environment?.cookies ? JSON.stringify(environment.cookies, null, 2) : '{}',
      admins: (environment?.admins as string[]) || [],
      users: (environment?.users as string[]) || null,
      isPublic: !environment?.users, // 如果users为null，表示公开
    },
    validate: {
      name: value => (!value ? '环境名称不能为空' : null),
      baseUrl: value => {
        if (!value) return '基础URL不能为空'
        try {
          new URL(value)
          return null
        } catch {
          return '请输入有效的URL'
        }
      },
      headers: value => {
        try {
          JSON.parse(value)
          return null
        } catch {
          return '请输入有效的JSON格式'
        }
      },
      cookies: value => {
        try {
          JSON.parse(value)
          return null
        } catch {
          return '请输入有效的JSON格式'
        }
      },
    },
  })

  const createMutation = useMutation(
    trpc.environment.create.mutationOptions({
      onSuccess: () => {
        notifications.show({
          title: '创建成功',
          message: '环境已创建',
          color: 'green',
        })
        onSuccess?.()
        onClose()
      },
      onError: error => {
        notifications.show({
          title: '创建失败',
          message: error.message,
          color: 'red',
        })
      },
    })
  )

  const updateMutation = useMutation(
    trpc.environment.update.mutationOptions({
      onSuccess: () => {
        notifications.show({
          title: '更新成功',
          message: '环境已更新',
          color: 'green',
        })
        onSuccess?.()
        onClose()
      },
      onError: error => {
        notifications.show({
          title: '更新失败',
          message: error.message,
          color: 'red',
        })
      },
    })
  )

  const handleSubmit = form.onSubmit(values => {
    try {
      const headers = JSON.parse(values.headers)
      const cookies = JSON.parse(values.cookies)

      const data = {
        name: values.name,
        description: values.description || undefined,
        baseUrl: values.baseUrl,
        headers,
        cookies,
        admins: values.admins,
        users: values.isPublic ? undefined : values.users,
      }

      if (isEditing) {
        updateMutation.mutate({
          id: environment.id,
          data,
        })
      } else {
        createMutation.mutate(data)
      }
    } catch (error) {
      notifications.show({
        title: '数据格式错误',
        message: '请检查JSON格式是否正确',
        color: 'red',
      })
    }
  })

  const isLoading = createMutation.isPending || updateMutation.isPending

  return (
    <Modal
      opened
      onClose={onClose}
      title={isEditing ? '编辑环境' : '创建环境'}
      size="lg"
      centered
    >
      <form onSubmit={handleSubmit}>
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="basic" leftSection={<IconServer size={16} />}>
              基本信息
            </Tabs.Tab>
            <Tabs.Tab value="config" leftSection={<IconSettings size={16} />}>
              配置信息
            </Tabs.Tab>
            <Tabs.Tab value="permissions" leftSection={<IconUsers size={16} />}>
              权限管理
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="basic" pt="md">
            <Stack gap="md">
              <TextInput
                label="环境名称"
                placeholder="例如：测试环境"
                required
                {...form.getInputProps('name')}
              />

              <Textarea
                label="环境描述"
                placeholder="环境的详细描述（可选）"
                rows={3}
                {...form.getInputProps('description')}
              />

              <TextInput
                label="基础URL"
                placeholder="https://api.example.com"
                required
                {...form.getInputProps('baseUrl')}
              />
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="config" pt="md">
            <Stack gap="md">
              <JsonInput
                label="默认请求头"
                description="JSON格式的默认请求头配置"
                placeholder='{"Content-Type": "application/json"}'
                validationError="请输入有效的JSON格式"
                formatOnBlur
                autosize
                minRows={4}
                maxRows={10}
                {...form.getInputProps('headers')}
              />

              <JsonInput
                label="Cookie配置"
                description="JSON格式的Cookie配置，支持登录后自动保存"
                placeholder='{"session": "xxx", "token": "yyy"}'
                validationError="请输入有效的JSON格式"
                formatOnBlur
                autosize
                minRows={4}
                maxRows={10}
                {...form.getInputProps('cookies')}
              />
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="permissions" pt="md">
            <Stack gap="md">
              <Switch
                label="公开环境"
                description="开启后，所有用户都可以使用此环境"
                {...form.getInputProps('isPublic', { type: 'checkbox' })}
              />

              {!form.values.isPublic && (
                <MultiSelect
                  label="使用者"
                  description="选择可以使用此环境的用户"
                  placeholder="选择用户"
                  data={users.map((user: any) => ({
                    value: user.username,
                    label: user.chineseName || user.username,
                  }))}
                  searchable
                  {...form.getInputProps('users')}
                />
              )}

              <MultiSelect
                label="管理员"
                description="选择可以管理此环境的用户（除创建者外）"
                placeholder="选择管理员"
                data={users.map((user: any) => ({
                  value: user.username,
                  label: user.chineseName || user.username,
                }))}
                searchable
                {...form.getInputProps('admins')}
              />

              <Alert icon={<IconAlertCircle size="1rem" />} color="blue">
                <div>
                  <strong>权限说明：</strong>
                  <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                    <li>创建者拥有所有权限，包括删除环境</li>
                    <li>管理员可以编辑环境配置和权限</li>
                    <li>使用者只能使用环境进行调试</li>
                    <li>公开环境所有用户都可以使用</li>
                  </ul>
                </div>
              </Alert>
            </Stack>
          </Tabs.Panel>
        </Tabs>

        <Group justify="flex-end" mt="xl">
          <Button variant="subtle" onClick={onClose} disabled={isLoading}>
            取消
          </Button>
          <Button type="submit" loading={isLoading}>
            {isEditing ? '更新' : '创建'}
          </Button>
        </Group>
      </form>
    </Modal>
  )
}
