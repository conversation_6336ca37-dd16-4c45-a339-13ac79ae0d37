import React, { useEffect, useMemo, useState, type SVGProps } from 'react'
import {
  Box,
  Text,
  Group,
  Stack,
  Badge,
  Paper,
  Code,
  Tabs,
  ScrollArea,
  CopyButton,
  ActionIcon,
  Accordion,
  ThemeIcon,
  useMantineColorScheme,
  List,
  Divider,
  Button,
  Popover,
  MultiSelect,
  Select,
} from '@mantine/core'
import { TemplateManager } from '../template/TemplateManager'
import {
  IconArrowRight,
  IconCopy,
  IconCheck,
  IconBrandTypescript,
  IconNumber,
  IconList,
  IconQuestionMark,
  IconHttpGet,
  IconHttpPost,
  IconHttpPut,
  IconHttpDelete,
  IconArrowsUpDown,
  IconJson,
  IconChevronRight,
  IconCircleFilled,
  IconPointFilled,
  IconFolder,
  IconPlus,
  IconEdit,
  IconTrash,
  IconInfoCircle,
  IconRoute,
  IconTag,
  IconBug,
} from '@tabler/icons-react'
import { CodeHighlight } from '@mantine/code-highlight'
import { getRoute<PERSON><PERSON>, useMatches, useNavigate, Link } from '@tanstack/react-router'
import { safeJsonParse } from '../../../../server/src/utils/common'
import { MessageStructure } from './MessageStructure'
import { extractComponents, type ExtractComponentsResult, type MethodDetailProps } from './common'
import { SearchSpotlight } from './SearchSpotlight'
import { generateTsInterface } from '@/utils/generate'
import { notifications } from '@mantine/notifications'
import { TimeRangeSelector } from './TimeRangeSelector'
import {
  type ChangeType,
  type TimeRange,
  type ComparisonSettings,
  getChangeTypeColor,
  getComparisonSettings,
  saveComparisonSettings,
} from '@/utils/diffUtils'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { StandaloneTagsEditor, StandalonePathRuleEditor } from './InterfaceMetadata'
import { InterfaceDebugTab } from '../debug/InterfaceDebugTab'

// 查找消息类型的辅助函数
export const findMessageOrEnum = (name: string, collection: Record<string, any>) => {
  // 1. 直接匹配
  if (collection[name]) {
    return { item: collection[name], key: name }
  }

  // 外部类型，必须全匹配
  if (name.includes('.')) {
    return { item: null, key: null }
  }

  // 2. 尝试移除包名前缀
  const shortName = name.split('.').pop() || ''
  if (shortName && collection[shortName]) {
    return { item: collection[shortName], key: shortName }
  }

  // 3. 尝试在所有消息中查找匹配的完全限定名称
  const matchingKey = Object.keys(collection).find(key => {
    // 完全匹配
    if (key === name) return true

    // 短名称匹配
    const keyParts = key.split('.')
    const nameParts = name.split('.')
    return keyParts[keyParts.length - 1] === nameParts[nameParts.length - 1]
  })

  if (matchingKey) {
    return { item: collection[matchingKey], key: matchingKey }
  }

  return { item: null, key: null }
}

const MethodDetail: React.FC<MethodDetailProps> = ({
  serviceName,
  methodName,
  method,
  messages,
  enums,
  services,
  serviceNames,
  shortServices,
  shortServiceNames,
  timeRange,
  getMessageChangeType,
  getFieldChangeType,
  projectId,
  serviceProtoMapping,
}) => {
  const { requestType, responseType, parsedOptions = [], comment } = method
  const swaggerOptions = parsedOptions.find((opt: any) => opt['(trpc.swagger)']) || {}
  const swagger = swaggerOptions['(trpc.swagger)'] || {}

  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  // 获取请求和响应类型的变更状态
  const requestTypeChangeType =
    timeRange && getMessageChangeType ? getMessageChangeType(requestType) : 'unchanged'

  const responseTypeChangeType =
    timeRange && getMessageChangeType ? getMessageChangeType(responseType) : 'unchanged'

  // 使用映射直接获取 ProtoFile 信息
  const protoFileInfo = serviceProtoMapping?.[serviceName]
  const protoFileId = protoFileInfo?.fileId || 0

  // 检查是否有有效的 ProtoFile 信息
  const hasValidProtoFile = protoFileId > 0

  // 生成示例请求和响应
  const generateExampleJson = (typeName: string) => {
    return '{}'
    // 查找消息类型
    const { item: message } = findMessageOrEnum(typeName, messages)
    if (!message || !message.fields) return '{}'

    const example: Record<string, any> = {}

    Object.entries(message.fields).forEach(([fieldName, fieldData]: [string, any]) => {
      const { type, rule } = fieldData as { type: string; rule?: string }

      // 查找嵌套类型
      const { item: nestedMessage } = findMessageOrEnum(type, messages)
      const { item: enumItem } = findMessageOrEnum(type, enums)

      if (rule === 'repeated') {
        if (nestedMessage) {
          // 嵌套对象数组
          example[fieldName] = [JSON.parse(generateExampleJson(type))]
        } else if (type.startsWith('int')) {
          example[fieldName] = [1, 2, 3]
        } else if (type === 'string') {
          example[fieldName] = ['示例文本']
        } else if (type === 'bool') {
          example[fieldName] = [true]
        } else {
          example[fieldName] = []
        }
      } else if (nestedMessage) {
        // 嵌套对象
        example[fieldName] = JSON.parse(generateExampleJson(type))
      } else if (enumItem) {
        // 枚举值
        const enumValues = Object.values(enumItem.values)
        example[fieldName] = enumValues.length > 0 ? enumValues[0] : 0
      } else if (type.startsWith('int')) {
        example[fieldName] = 123
      } else if (type === 'string') {
        example[fieldName] = '示例文本'
      } else if (type === 'bool') {
        example[fieldName] = true
      } else {
        example[fieldName] = null
      }
    })

    return JSON.stringify(example, null, 2)
  }

  const copyTsInterface = (type: string) => {
    // 生成 TypeScript 接口定义
    const data = {
      messages,
      enums,
      services: {},
      serviceNames: [],
      shortServices: {},
      shortServiceNames: [],
      serviceProtoMapping: {},
    }

    const tsCode = generateTsInterface({
      data,
      projectId: 0,
      structNames: [type],
      deep: false, // 不包含嵌套类型
    })

    console.log({ tsCode })

    // 复制到剪贴板
    navigator.clipboard.writeText(tsCode)

    const shortType = messages[type] ? type.split('.').at(-1) : type

    // 显示通知
    notifications.show({
      title: '复制成功',
      message: `已复制 ${shortType} 的 TypeScript 类型定义`,
      color: 'teal',
    })
  }

  const shortRequestType = messages[requestType] ? requestType.split('.').at(-1) : requestType
  const shortResponseType = messages[responseType] ? responseType.split('.').at(-1) : responseType

  return (
    <Stack gap="md">
      <Stack gap="xs">
        <Group justify="space-between">
          <Group>
            <Text fw={700} size="lg">
              {swagger.title || comment || methodName}
            </Text>

            {/* 标签编辑器 */}
            {hasValidProtoFile && projectId && (
              <StandaloneTagsEditor
                projectId={projectId}
                protoFileId={protoFileId}
                serviceName={serviceName}
                methodName={methodName}
                hasValidProtoFile={hasValidProtoFile}
              />
            )}
          </Group>

          <Group>
            {/* 添加模板管理器 */}
            <TemplateManager
              apiData={{
                serviceName,
                methodName,
              }}
              projectId={projectId!}
            />
          </Group>
        </Group>

        {swagger.description &&
          swagger.description !== (swagger.title || comment || methodName) && (
            <Text c="dimmed" size="sm">
              {swagger.description}
            </Text>
          )}

        {/* 接口路径编辑器 */}
        {hasValidProtoFile && projectId && (
          <StandalonePathRuleEditor
            projectId={projectId}
            protoFileId={protoFileId}
            serviceName={serviceName}
            methodName={methodName}
            hasValidProtoFile={hasValidProtoFile}
          />
        )}
      </Stack>

      {/* 显示错误信息 - 由 StandalonePathRuleEditor 组件处理 */}

      <Tabs defaultValue="struct" keepMounted={false}>
        <Tabs.List>
          <Tabs.Tab value="struct" leftSection={<IconArrowsUpDown size={14} />}>
            参数结构
          </Tabs.Tab>
          {hasValidProtoFile && projectId && (
            <Tabs.Tab value="debug" leftSection={<IconBug size={14} />}>
              接口测试
            </Tabs.Tab>
          )}
          {/* <Tabs.Tab value="example" leftSection={<IconJson size={14} />}>
            示例代码
          </Tabs.Tab> */}
        </Tabs.List>

        <Tabs.Panel value="struct" pt="md">
          <Stack gap="md">
            <Group>
              <Text fw={700}>请求类型:</Text>
              <Badge
                variant="outline"
                tt="none"
                className="!cursor-pointer"
                onClick={() => copyTsInterface(requestType)}
                style={{
                  backgroundColor:
                    timeRange && requestTypeChangeType !== 'unchanged'
                      ? getChangeTypeColor(requestTypeChangeType, isDark)
                      : undefined,
                }}
              >
                {shortRequestType}
                {timeRange && requestTypeChangeType === 'added' && (
                  <IconPlus size={14} style={{ marginLeft: 4 }} />
                )}
                {timeRange && requestTypeChangeType === 'modified' && (
                  <IconEdit size={14} style={{ marginLeft: 4 }} />
                )}
                {timeRange && requestTypeChangeType === 'deleted' && (
                  <IconTrash size={14} style={{ marginLeft: 4 }} />
                )}
              </Badge>
            </Group>

            <MessageStructure
              messageName={requestType}
              messages={messages}
              enums={enums}
              timeRange={timeRange}
              getMessageChangeType={getMessageChangeType}
              getFieldChangeType={getFieldChangeType}
            />

            <Group>
              <Text fw={700}>响应类型:</Text>
              <Badge
                variant="outline"
                tt="none"
                className="!cursor-pointer"
                onClick={() => copyTsInterface(responseType)}
                style={{
                  backgroundColor:
                    timeRange && responseTypeChangeType !== 'unchanged'
                      ? getChangeTypeColor(responseTypeChangeType, isDark)
                      : undefined,
                }}
              >
                {shortResponseType}
                {timeRange && responseTypeChangeType === 'added' && (
                  <IconPlus size={14} style={{ marginLeft: 4 }} />
                )}
                {timeRange && responseTypeChangeType === 'modified' && (
                  <IconEdit size={14} style={{ marginLeft: 4 }} />
                )}
                {timeRange && responseTypeChangeType === 'deleted' && (
                  <IconTrash size={14} style={{ marginLeft: 4 }} />
                )}
              </Badge>
            </Group>

            <MessageStructure
              messageName={responseType}
              messages={messages}
              enums={enums}
              timeRange={timeRange}
              getMessageChangeType={getMessageChangeType}
              getFieldChangeType={getFieldChangeType}
            />
          </Stack>
        </Tabs.Panel>

        {hasValidProtoFile && projectId && (
          <Tabs.Panel value="debug" pt="md">
            <InterfaceDebugTab
              projectId={projectId}
              protoFileId={protoFileId}
              serviceName={serviceName}
              methodName={methodName}
            />
          </Tabs.Panel>
        )}

        <Tabs.Panel value="example" pt="md">
          <Tabs defaultValue="request-example" keepMounted={false}>
            <Tabs.List>
              <Tabs.Tab value="request-example">请求示例</Tabs.Tab>
              <Tabs.Tab value="response-example">响应示例</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="request-example" pt="md">
              <Paper pos="relative" withBorder>
                <CodeHighlight
                  language="json"
                  code={generateExampleJson(requestType)}
                  withCopyButton={false}
                />
                <CopyButton value={generateExampleJson(requestType)}>
                  {({ copied, copy }) => (
                    <ActionIcon
                      pos="absolute"
                      top={8}
                      right={8}
                      onClick={copy}
                      color={copied ? 'teal' : 'gray'}
                    >
                      {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    </ActionIcon>
                  )}
                </CopyButton>
              </Paper>
            </Tabs.Panel>

            <Tabs.Panel value="response-example" pt="md">
              <Paper pos="relative" withBorder>
                <CodeHighlight
                  language="json"
                  code={generateExampleJson(responseType)}
                  withCopyButton={false}
                />
                <CopyButton value={generateExampleJson(responseType)}>
                  {({ copied, copy }) => (
                    <ActionIcon
                      pos="absolute"
                      top={8}
                      right={8}
                      onClick={copy}
                      color={copied ? 'teal' : 'gray'}
                    >
                      {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                    </ActionIcon>
                  )}
                </CopyButton>
              </Paper>
            </Tabs.Panel>
          </Tabs>
        </Tabs.Panel>
      </Tabs>
    </Stack>
  )
}

const routePackage = getRouteApi('/$projectId/api/$service/$method')

// API 文档查看器组件
interface ApiDocViewerProps {
  parsedResultList: string[]
  extraParsedResultList: string[]
  projectId: number
}

const ApiDocViewer: React.FC<ApiDocViewerProps> = ({
  parsedResultList,
  extraParsedResultList,
  projectId,
}) => {
  const matches = useMatches()
  const match = matches.find(match => match.fullPath === routePackage.id)
  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  // 获取项目详情以获取 protoFiles 信息
  const { data: project } = useQuery(trpc.project.info.queryOptions({ projectId }))

  // 时间范围状态
  const [timeRange, setTimeRange] = useState<TimeRange | null>(null)

  // 对比设置状态
  const [comparisonSettings, setComparisonSettings] =
    useState<ComparisonSettings>(getComparisonSettings())

  // 处理对比设置变更
  const handleComparisonChange = (settings: ComparisonSettings) => {
    setComparisonSettings(settings)
    saveComparisonSettings(settings)
  }

  // 获取项目中所有 Proto 文件的变更历史数据
  const { data: projectChangeHistory, isLoading: isLoadingHistory } = useQuery(
    trpc.proto.getProjectHistoriesWithinTimeRange.queryOptions(
      {
        projectId: projectId,
        days:
          typeof timeRange === 'number'
            ? timeRange
            : timeRange === 'day'
              ? 1
              : timeRange === 'week'
                ? 7
                : timeRange === 'month'
                  ? 30
                  : 7,
      },
      {
        enabled: !!timeRange && projectId > 0,
        staleTime: 1000 * 60 * 5, // 5分钟缓存
      },
    ),
  )

  // 提取组件
  const all = useMemo(() => {
    console.log('run parse all')
    const start = performance.now()
    const list = parsedResultList.map((result, index) => {
      const json = safeJsonParse<any>(result)

      // 在解析时传入文件信息
      const fileInfo = project?.files?.[index]
        ? {
            fileId: project.files[index].id,
            fileName: project.files[index].name,
          }
        : undefined

      return extractComponents(json?.root?.nested || {}, fileInfo)
    })

    const extraList = extraParsedResultList.map((result, index) => {
      const json = safeJsonParse<any>(result)
      return extractComponents(json?.root?.nested || {}, undefined)
    })

    const all = list.reduce(
      (acc, cur) => {
        return {
          messages: { ...acc.messages, ...cur.messages },
          enums: { ...acc.enums, ...cur.enums },
          services: { ...acc.services, ...cur.services },
          serviceNames: [...new Set([...acc.serviceNames, ...cur.serviceNames])],
          shortServices: { ...acc.shortServices, ...cur.shortServices },
          shortServiceNames: [...new Set([...acc.shortServiceNames, ...cur.shortServiceNames])],
          serviceProtoMapping: { ...acc.serviceProtoMapping, ...cur.serviceProtoMapping },
        }
      },
      {
        messages: {},
        enums: {},
        services: {},
        serviceNames: [],
        shortServices: {},
        shortServiceNames: [],
        serviceProtoMapping: {},
      } as (typeof list)[number],
    )
    const allWithExtra = extraList.reduce(
      (acc, cur) => {
        return {
          messages: { ...acc.messages, ...cur.messages },
          enums: { ...acc.enums, ...cur.enums },
          services: { ...acc.services },
          serviceNames: acc.serviceNames,
          shortServices: { ...acc.shortServices },
          shortServiceNames: acc.shortServiceNames,
          serviceProtoMapping: { ...acc.serviceProtoMapping },
        }
      },
      { ...all } as (typeof list)[number],
    )
    const end = performance.now()
    console.log(`run parse all time: ${end - start}ms`)
    return allWithExtra
  }, [parsedResultList.length, project?.files])

  // 查找包含特定服务或消息的 Proto 文件变更
  const findProtoChangeForService = (serviceName: string) => {
    if (!projectChangeHistory || !projectChangeHistory.protoChanges) return null

    // 遍历所有 Proto 文件的变更，查找包含指定服务的文件
    return projectChangeHistory.protoChanges.find(
      protoChange => protoChange.changes && (protoChange.changes.services as any)[serviceName],
    )
  }

  const findProtoChangeForMessage = (messageName: string) => {
    if (!projectChangeHistory || !projectChangeHistory.protoChanges) return null

    // 遍历所有 Proto 文件的变更，查找包含指定消息的文件
    return projectChangeHistory.protoChanges.find(
      protoChange => protoChange.changes && (protoChange.changes.messages as any)[messageName],
    )
  }

  // 获取服务和方法的变更状态
  const getServiceChangeType = (serviceName: string): ChangeType => {
    const protoChange = findProtoChangeForService(serviceName)
    if (!protoChange || !protoChange.changes) return 'unchanged'
    return (
      ((protoChange.changes.services as any)[serviceName]?.changeType as ChangeType) || 'unchanged'
    )
  }

  const getMethodChangeType = (serviceName: string, methodName: string): ChangeType => {
    const protoChange = findProtoChangeForService(serviceName)
    if (!protoChange || !protoChange.changes) return 'unchanged'
    return (
      ((protoChange.changes.methods as any)[serviceName]?.[methodName]?.changeType as ChangeType) ||
      'unchanged'
    )
  }

  // 获取消息的变更状态
  const getMessageChangeType = (messageName: string): ChangeType => {
    const protoChange = findProtoChangeForMessage(messageName)
    if (!protoChange || !protoChange.changes) return 'unchanged'
    return (
      ((protoChange.changes.messages as any)[messageName]?.changeType as ChangeType) || 'unchanged'
    )
  }

  // 获取字段的变更状态
  const getFieldChangeType = (messageName: string, fieldName: string): ChangeType => {
    const protoChange = findProtoChangeForMessage(messageName)
    if (!protoChange || !protoChange.changes) return 'unchanged'
    return (
      ((protoChange.changes.messages as any)[messageName]?.fields?.[fieldName]
        ?.changeType as ChangeType) || 'unchanged'
    )
  }

  return (
    <Stack gap="md" className="w-full">
      <Group align="flex-start" grow className="w-full !mt=0" wrap="nowrap" h="100%">
        {/* 左侧接口列表 */}
        <ApiDocMenu
          all={all}
          projectId={projectId}
          timeRange={timeRange}
          getMethodChangeType={getMethodChangeType}
          onTimeRangeChange={setTimeRange}
          isLoadingHistory={isLoadingHistory}
          comparisonSettings={comparisonSettings}
          onComparisonChange={handleComparisonChange}
        />
        {/* 右侧方法详情 */}
        <Box style={{ flex: 1 }} maw="10000px">
          <ScrollArea scrollbarSize={6} h="calc(100vh - 140px)">
            {match ? (
              <MethodDetail
                serviceName={match.params.service}
                methodName={match.params.method}
                method={all.shortServices[match.params.service].methods[match.params.method]}
                messages={all.messages}
                enums={all.enums}
                services={all.services}
                serviceNames={all.serviceNames}
                shortServices={all.shortServices}
                shortServiceNames={all.shortServiceNames}
                timeRange={timeRange}
                getMessageChangeType={getMessageChangeType}
                getFieldChangeType={getFieldChangeType}
                projectId={projectId}
                serviceProtoMapping={all.serviceProtoMapping}
              />
            ) : (
              <Paper p="xl" withBorder ta="center">
                <Text c="dimmed">请从左侧选择一个接口查看详情</Text>
              </Paper>
            )}
          </ScrollArea>
        </Box>
      </Group>
    </Stack>
  )
}

interface ApiDocMenuProps {
  all: ExtractComponentsResult
  projectId: number
  timeRange: TimeRange | null
  getMethodChangeType: (serviceName: string, methodName: string) => ChangeType
  onTimeRangeChange: (value: TimeRange | null) => void
  isLoadingHistory: boolean
  comparisonSettings: ComparisonSettings
  onComparisonChange: (settings: ComparisonSettings) => void
}

const ApiDocMenu: React.FC<ApiDocMenuProps> = ({
  all,
  projectId,
  timeRange,
  getMethodChangeType,
  onTimeRangeChange,
  isLoadingHistory,
  comparisonSettings,
  onComparisonChange,
}) => {
  const matches = useMatches()
  const match = matches.find(match => match.fullPath === routePackage.id)

  const [openedService, setOpenedService] = useState<string | null>(
    match ? match.params.service : null,
  )

  const navigate = useNavigate()

  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  useEffect(() => {
    if (!match && all.shortServiceNames.length > 0) {
      const firstService = all.shortServiceNames[0]
      const firstMethod = Object.keys(all.shortServices[firstService]?.methods ?? {})[0]
      if (firstService && firstMethod) {
        navigate({
          to: '/$projectId/api/$service/$method',
          params: {
            projectId: projectId + '',
            service: firstService,
            method: firstMethod,
          },
        })
        setOpenedService(firstService)
      }
    }
  }, [match, all.serviceNames.length])

  // 获取变更类型对应的图标
  const getChangeTypeIcon = (changeType: ChangeType) => {
    switch (changeType) {
      case 'added':
        return <IconPlus size={14} color={isDark ? '#81c784' : '#2e7d32'} />
      case 'modified':
        return <IconEdit size={14} color={isDark ? '#64b5f6' : '#1565c0'} />
      case 'deleted':
        return <IconTrash size={14} color={isDark ? '#e57373' : '#c62828'} />
      default:
        return <IconPointFilled size={12} />
    }
  }

  return (
    <Box w="calc(20vw + 50px)" maw="400px" className="!flex-none h-full flex flex-col gap-[8px]">
      <Group gap="xs" align="center" wrap="nowrap">
        <Box style={{ flex: 1 }}>
          <SearchSpotlight data={all} projectId={projectId} />
        </Box>
        {/* <TimeRangeSelector
          value={timeRange}
          onChange={onTimeRangeChange}
          isLoading={isLoadingHistory}
          comparisonSettings={comparisonSettings}
          onComparisonChange={onComparisonChange}
        /> */}
      </Group>
      <ScrollArea scrollbarSize={6} h="calc(100vh - 180px)">
        <Accordion value={openedService} variant="default" onChange={setOpenedService}>
          {all.shortServiceNames.map(shortName => {
            const service = all.shortServices[shortName]
            const methods = service.methods || {}
            const methodNames = Object.keys(methods)

            // 根据方法的变更状态来确定服务的变更状态
            let serviceChangeType: ChangeType = 'unchanged'

            // 检查是否有选中的方法属于这个服务
            const hasSelectedMethod = match && match.params.service === shortName

            // 获取所有方法的变更状态
            const methodChangeTypes = timeRange
              ? methodNames.map(methodName => getMethodChangeType(shortName, methodName))
              : []

            // 检查是否有变更的方法
            const hasChangedMethods = methodChangeTypes.some(type => type !== 'unchanged')

            // 如果启用了"仅显示变更接口"，且当前服务没有变更的方法，且没有选中的方法，则跳过
            if (
              comparisonSettings.enabled &&
              comparisonSettings.showChangedOnly &&
              !hasChangedMethods &&
              !hasSelectedMethod
            ) {
              return null
            }

            if (timeRange) {
              // 如果有任何方法被删除，则服务标记为删除
              if (methodChangeTypes.includes('deleted')) {
                serviceChangeType = 'deleted'
              }
              // 否则，如果有任何方法被添加，则服务标记为添加
              else if (methodChangeTypes.includes('added')) {
                serviceChangeType = 'added'
              }
              // 否则，如果有任何方法被修改，则服务标记为修改
              else if (methodChangeTypes.includes('modified')) {
                serviceChangeType = 'modified'
              }
            }

            return (
              <Accordion.Item key={shortName} value={shortName} bd={0}>
                <Accordion.Control classNames={{ label: '!py-[4px]' }}>
                  <Group gap="sm">
                    <IconFolder size={22} />
                    <Group gap={5}>
                      <Text fw={700} size="sm">
                        {shortName}
                      </Text>
                      {timeRange &&
                        serviceChangeType !== 'unchanged' &&
                        getChangeTypeIcon(serviceChangeType)}
                    </Group>
                  </Group>
                </Accordion.Control>
                <Accordion.Panel classNames={{ content: '!pl-[12px]' }}>
                  <div>
                    {methodNames.map(methodName => {
                      const method = methods[methodName]
                      const swaggerOptions =
                        method.parsedOptions?.find((opt: any) => opt['(trpc.swagger)']) || {}
                      const swagger = swaggerOptions['(trpc.swagger)'] || {}
                      const isSelected =
                        match &&
                        match.params.service === shortName &&
                        match.params.method === methodName

                      const methodChangeType = timeRange
                        ? getMethodChangeType(shortName, methodName)
                        : 'unchanged'

                      // 如果启用了"仅显示变更接口"，且当前方法没有变更，且不是当前选中的方法，则跳过
                      if (
                        comparisonSettings.enabled &&
                        comparisonSettings.showChangedOnly &&
                        methodChangeType === 'unchanged' &&
                        !isSelected
                      ) {
                        return null
                      }

                      // 变更图标会直接在渲染时使用

                      // 如果是删除的方法且未选中，则使用删除线样式
                      const isDeleted = timeRange && methodChangeType === 'deleted'

                      // 根据变更类型设置背景色
                      let bgColor = isSelected
                        ? isDark
                          ? 'var(--mantine-color-blue-9)'
                          : 'var(--mantine-color-blue-2)'
                        : 'transparent'

                      // 如果有变更且未选中，则使用淡色背景
                      if (timeRange && methodChangeType !== 'unchanged' && !isSelected) {
                        bgColor = getChangeTypeColor(methodChangeType, isDark)
                      }

                      // 文本颜色
                      let textColor = isSelected
                        ? isDark
                          ? 'white'
                          : 'var(--mantine-color-blue-9)'
                        : undefined

                      // 如果有变更且未选中，则使用对应的文本颜色
                      if (timeRange && methodChangeType !== 'unchanged' && !isSelected) {
                        textColor = isDark ? 'white' : undefined
                      }

                      return (
                        <Link
                          to={`/$projectId/api/$service/$method`}
                          params={{
                            method: methodName,
                            service: shortName,
                            projectId: projectId + '',
                          }}
                          key={methodName}
                          className="rounded-[4px] px-[8px] py-[4px] cursor-pointer flex items-center gap-2"
                          style={{
                            backgroundColor: bgColor,
                            textDecoration: isDeleted && !isSelected ? 'line-through' : 'none',
                            color: textColor,
                          }}
                        >
                          {/* 变更图标或点 */}
                          {timeRange && methodChangeType !== 'unchanged' ? (
                            getChangeTypeIcon(methodChangeType)
                          ) : (
                            <IconPointFilled size={12} />
                          )}

                          <span className="text-sm line-clamp-1 flex-1">
                            {swagger.title || method['comment'] || methodName}
                          </span>
                        </Link>
                      )
                    })}
                  </div>
                </Accordion.Panel>
              </Accordion.Item>
            )
          })}
        </Accordion>
      </ScrollArea>
    </Box>
  )
}

export default ApiDocViewer
