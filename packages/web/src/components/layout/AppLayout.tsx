import { useEffect, type ReactNode } from 'react'
import {
  AppShell,
  Group,
  Title,
  Button,
  Text,
  Avatar,
  Menu,
  Divider,
  Box,
  Burger,
  rem,
  useMantineTheme,
  ActionIcon,
  useMantineColorScheme,
} from '@mantine/core'
import { getRouteApi, Link, useLocation, useMatches } from '@tanstack/react-router'
import { AppNavbar } from './AppNavbar'
import {
  IconChevronDown,
  IconLogout,
  IconMoon,
  IconSettings,
  IconSun,
  IconUser,
} from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { useDisclosure } from '@mantine/hooks'
import { VscodeIconsFileTypeProtobuf } from '../common/icons'

interface AppLayoutProps {
  children: ReactNode
}

const routeProtoDetail = getRouteApi('/$projectId')

export function AppLayout({ children }: AppLayoutProps) {
  // 获取当前用户信息
  const { data: currentUser } = useQuery(trpc.user.getCurrentUser.queryOptions())
  const [opened, { toggle, close }] = useDisclosure(true)

  // 判断当前是不是在 Proto 详情页
  const matches = useMatches()

  const isProtoDetail = matches.some(match => match.fullPath === routeProtoDetail.id)
  useEffect(() => {
    if (isProtoDetail && opened) {
      close()
    } else if (!isProtoDetail && !opened) {
      toggle()
    }
  }, [isProtoDetail])

  return (
    <AppShell
      // header={{ height: 60 }}
      navbar={{
        width: opened ? 240 : 74,
        breakpoint: 0,
        // collapsed: { mobile: !opened, desktop: !opened },
      }}
      padding="md"
    >
      {/* <AppShell.Header p="xs">
        <Group justify="space-between" h="100%" px="sm">
          <Group component={Link} {...{ to: '/' }}>
            <VscodeIconsFileTypeProtobuf className="text-[40px]" />
            <Title order={3}>ProtoAPI</Title>
          </Group>

          <Group>
            <ThemeToggle />
            {currentUser ? (
              <Menu position="bottom-end" withArrow>
                <Menu.Target>
                  <Box style={{ cursor: 'pointer' }}>
                    <Group gap="xs">
                      <Avatar color="blue" radius="xl">
                        {currentUser.username.substring(0, 2).toUpperCase()}
                      </Avatar>
                      <div>
                        <Text size="sm" fw={500}>
                          {currentUser.username}
                        </Text>
                      </div>
                      <IconChevronDown size={16} />
                    </Group>
                  </Box>
                </Menu.Target>

                <Menu.Dropdown>
                  <Menu.Item leftSection={<IconUser size={16} />}>个人资料</Menu.Item>
                  <Menu.Item leftSection={<IconSettings size={16} />}>设置</Menu.Item>
                  <Divider />
                  <Menu.Item leftSection={<IconLogout size={16} />} color="red">
                    退出登录
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            ) : (
              <Button variant="light">登录</Button>
            )}
          </Group>
        </Group>
      </AppShell.Header> */}

      <AppShell.Navbar p={0}>
        <AppNavbar opened={opened} />
      </AppShell.Navbar>

      <AppShell.Main className="absolute inset-0">{children}</AppShell.Main>
    </AppShell>
  )
}

const ThemeToggle = () => {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()
  return (
    <ActionIcon onClick={toggleColorScheme} variant="light">
      {colorScheme === 'dark' ? <IconSun size={16} /> : <IconMoon size={16} />}
    </ActionIcon>
  )
}
