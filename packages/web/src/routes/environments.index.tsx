import { createFileRoute } from '@tanstack/react-router'
import {
  Container,
  Title,
  Button,
  Stack,
  Group,
  Card,
  Text,
  Badge,
  ActionIcon,
  Menu,
  Alert,
  Loader,
  Box,
} from '@mantine/core'
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconSettings,
  IconServer,
  IconInfoCircle,
} from '@tabler/icons-react'
import { useQuery } from '@tanstack/react-query'
import { trpc } from '@/utils/trpc'
import { useModal } from '@/utils/modal'
import { EnvironmentFormModal } from '@/components/environment/EnvironmentFormModal'
import { EnvironmentDeleteModal } from '@/components/environment/EnvironmentDeleteModal'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export const Route = createFileRoute('/environments/')({
  component: EnvironmentListPage,
})

function EnvironmentListPage() {
  // 获取环境列表
  const { data: environments, isLoading, refetch } = useQuery(trpc.environment.list.queryOptions())

  // 创建环境弹窗
  const environmentFormModal = useModal(EnvironmentFormModal)

  // 删除环境弹窗
  const environmentDeleteModal = useModal(EnvironmentDeleteModal)

  const handleCreateEnvironment = () => {
    environmentFormModal.open({
      onSuccess: () => {
        refetch()
      },
    })
  }

  const handleEditEnvironment = (environment: any) => {
    environmentFormModal.open({
      environment,
      onSuccess: () => {
        refetch()
      },
    })
  }

  const handleDeleteEnvironment = (environment: any) => {
    environmentDeleteModal.open({
      environment,
      onSuccess: () => {
        refetch()
      },
    })
  }

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Group justify="center">
          <Loader size="md" />
        </Group>
      </Container>
    )
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="lg">
        {/* 页面标题和操作 */}
        <Group justify="space-between">
          <div>
            <Title order={2}>环境管理</Title>
            <Text c="dimmed" size="sm">
              管理调试环境配置，支持多项目复用
            </Text>
          </div>
          <Button leftSection={<IconPlus size={16} />} onClick={handleCreateEnvironment}>
            创建环境
          </Button>
        </Group>

        {/* 环境列表 */}
        {environments && environments.length > 0 ? (
          <Stack gap="md">
            {environments.map(environment => (
              <EnvironmentCard
                key={environment.id}
                environment={environment}
                onEdit={handleEditEnvironment}
                onDelete={handleDeleteEnvironment}
              />
            ))}
          </Stack>
        ) : (
          <Alert icon={<IconInfoCircle size="1rem" />} title="暂无环境" color="blue">
            <Text size="sm">
              还没有创建任何调试环境。点击"创建环境"按钮开始配置您的第一个调试环境。
            </Text>
          </Alert>
        )}
      </Stack>

      {/* 弹窗组件 */}
      {environmentFormModal.component}
      {environmentDeleteModal.component}
    </Container>
  )
}

interface EnvironmentCardProps {
  environment: any
  onEdit: (environment: any) => void
  onDelete: (environment: any) => void
}

function EnvironmentCard({ environment, onEdit, onDelete }: EnvironmentCardProps) {
  const getPermissionBadge = (permission: string) => {
    switch (permission) {
      case 'owner':
        return (
          <Badge color="blue" size="sm">
            创建者
          </Badge>
        )
      case 'admin':
        return (
          <Badge color="green" size="sm">
            管理员
          </Badge>
        )
      case 'user':
        return (
          <Badge color="gray" size="sm">
            使用者
          </Badge>
        )
      default:
        return null
    }
  }

  const canManage = environment.permission === 'owner' || environment.permission === 'admin'

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Group justify="space-between" mb="xs">
        <Group>
          <IconServer size={20} color="var(--mantine-color-blue-6)" />
          <div>
            <Text fw={500} size="lg">
              {environment.name}
            </Text>
            {environment.description && (
              <Text size="sm" c="dimmed">
                {environment.description}
              </Text>
            )}
          </div>
        </Group>
        <Group>
          {getPermissionBadge(environment.permission)}
          {canManage && (
            <Menu position="bottom-end" withArrow>
              <Menu.Target>
                <ActionIcon variant="subtle" color="gray">
                  <IconDots size={16} />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item leftSection={<IconEdit size={14} />} onClick={() => onEdit(environment)}>
                  编辑
                </Menu.Item>
                {environment.permission === 'owner' && (
                  <Menu.Item
                    leftSection={<IconTrash size={14} />}
                    color="red"
                    onClick={() => onDelete(environment)}
                  >
                    删除
                  </Menu.Item>
                )}
              </Menu.Dropdown>
            </Menu>
          )}
        </Group>
      </Group>

      <Stack gap="xs">
        <Group>
          <Text size="sm" c="dimmed" w={80}>
            基础URL:
          </Text>
          <Text size="sm" ff="monospace">
            {environment.baseUrl}
          </Text>
        </Group>

        <Group>
          <Text size="sm" c="dimmed" w={80}>
            创建者:
          </Text>
          <Text size="sm">
            {environment.createdByUser.chineseName || environment.createdByUser.username}
          </Text>
        </Group>

        <Group>
          <Text size="sm" c="dimmed" w={80}>
            创建时间:
          </Text>
          <Text size="sm">
            {formatDistanceToNow(new Date(environment.createdAt), {
              addSuffix: true,
              locale: zhCN,
            })}
          </Text>
        </Group>

        <Group>
          <Text size="sm" c="dimmed" w={80}>
            使用情况:
          </Text>
          <Group gap="xs">
            <Badge variant="light" size="sm">
              {environment._count.projectEnvironments} 个项目
            </Badge>
            <Badge variant="light" size="sm">
              {environment._count.debugCases} 个用例
            </Badge>
          </Group>
        </Group>
      </Stack>
    </Card>
  )
}
